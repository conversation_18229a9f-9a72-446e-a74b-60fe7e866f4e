{"compilerOptions": {"target": "es6", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "importHelpers": false, "moduleResolution": "node", "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "noFallthroughCasesInSwitch": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@public/*": ["./public/*"]}, "types": ["vite/client"]}, "include": ["src", "vite.config.ts"], "exclude": ["node_modules", "dist"]}