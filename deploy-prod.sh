now="$(date +'%Y-%m-%d %H:%M:%S')"
message="update $now"
git add .
git commit -m "$message"
git push

cp .env.dev .env
yarn build
zip -r dist.zip dist

scp ./dist.zip root@*************:/opt/luanlt/front-end/

ssh root@************* "
    cd /opt/luanlt/front-end/;
    rm -rf happy-shop.loyatyhub.xyz;
    rm -rf dist;
    unzip dist.zip;
    mv dist happy-shop.loyatyhub.xyz;
    rm -f dist.zip;
    exit;
"

rm -f ./dist.zip
