import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

export const useContentLang = (input: {
  name: string;
  nameVi?: string;
  nameCn?: string;
}) => {
  const { i18n } = useTranslation();

  // Translate content based on current language
  const getTranslatedContent = useCallback(() => {
    switch (i18n.language) {
      case 'vi':
        return input.nameVi || input.name; // Default to `name` if `nameVi` is not provided
      case 'cn':
        return input.nameCn || input.name; // Default to `name` if `nameCn` is not provided
      default:
        return input.name; // Default content
    }
  }, [i18n.language, input]);

  return getTranslatedContent();
};
