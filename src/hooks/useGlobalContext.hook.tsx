import { CartItem } from '@/types/CartItem';
import { getCartStorage, setCartStorage } from '@/utils/cartStorage';
import message from 'antd/es/message';
import { MessageInstance } from 'antd/es/message/interface';
import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  TransitionStartFunction,
  useContext,
  useEffect,
  useState,
  useTransition,
} from 'react';

interface GlobalContextState {
  ticket: string[];
  normal: string[];
  notify: MessageInstance;
  cart: CartItem[];
  isPending: boolean;

  handleUpdateCart: (item: CartItem, isReplace?: boolean) => void;
  handleRemove: (ids: string[]) => void;
  handleEmptyCart: () => void;
  setTicket: Dispatch<SetStateAction<string[]>>;
  setNormal: Dispatch<SetStateAction<string[]>>;
  startTransition: TransitionStartFunction;
}

const GlobalContext = createContext<GlobalContextState>({
  notify: message,
  cart: [],
  ticket: [],
  normal: [],
  isPending: false,

  handleUpdateCart: () => {},
  handleRemove: () => {},
  handleEmptyCart: () => {},
  setTicket: () => {},
  setNormal: () => {},
  startTransition: () => {},
});

export const useGlobalContext = () => useContext(GlobalContext);

export const GlobalProvider = ({ children }: { children: ReactNode }) => {
  const [notify, contextHolder] = message.useMessage();
  const [isPending, startTransition] = useTransition();

  const [cart, setCart] = useState<CartItem[]>([]);
  const [ticket, setTicket] = useState<string[]>([]);
  const [normal, setNormal] = useState<string[]>([]);

  const isEqual = (prev: CartItem, current: CartItem) =>
    [prev.id, prev.ticketId].join('-') ===
    [current.id, current.ticketId].join('-');

  const handleUpdateCart = (item: CartItem, isReplace?: boolean) => {
    // TODO: update cart items by cart_id of user ??
    // try {
    //   const updatedCart = [...cart];
    //   const existingIndex = updatedCart.findIndex((i) => i.id === item.id);
      
    //   if (existingIndex !== -1) {
    //     updatedCart[existingIndex].quantity = isReplace
    //       ? item.quantity
    //       : updatedCart[existingIndex].quantity + item.quantity;
    //   } else {
    //     updatedCart.push(item);
    //   }

    //   await CartService.updateCart(updatedCart);
    //   setCart(updatedCart);
    // } catch (error) {
    //   notify.error('Failed to update cart');
    // }
    setCart((prev) => {
      let items = [...prev];

      if (!items.find((i) => i.id === item.id)) {
        items.push(item);
      } else {
        items = items.map((i) => {
          if (isEqual(i, item)) {
            return {
              ...i,
              quantity: !isReplace ? i.quantity + item.quantity : item.quantity,
            };
          }

          return i;
        });
      }
      setCartStorage(items);

      return items;
    });
  };

  const handleRemove = (ids: string[]) => {
    // TODO: remove cart items by cart_id of user ??
    // try {
    //   await CartService.removeItems(ids);
    //   setCart((prev) => prev.filter((i) => !ids.includes(i.id)));
    // } catch (error) {
    //   notify.error('Failed to remove items');
    // }
    setCart((prev) => {
      const filtered = prev.filter(
        (i) => !ids.includes(i.id)
      );
      setCartStorage(filtered);

      return filtered;
    });
  };

  const handleEmptyCart = () => {
    setCart(() => {
      setCartStorage([]);
      return [];
    })
  }

  // useEffect(() => {
  //   // TODO: get cart items by cart_id of user ??
  //   // setCart(...)

  //   // Example
  //   const cartItems = getCartStorage();
  //   setCart(cartItems);
  // }, []);

  useEffect(() => {
    // TODO: get cart items by cart_id of user ??
    // setCart(...)
    // const fetchCart = async () => {
    //   try {
    //     const cartItems = await CartService.getCart();
    //     setCart(cartItems);
    //   } catch (error) {
    //     notify.error('Failed to fetch cart items');
    //   }
    // };

    // fetchCart();

    // Example
    const cartItems = getCartStorage();
    setCart(cartItems);
  }, []);

  const initialState: GlobalContextState = {
    normal,
    ticket,
    notify,
    cart,
    isPending,

    handleUpdateCart,
    handleRemove,
    handleEmptyCart,
    setTicket,
    setNormal,
    startTransition,
  };

  return (
    <GlobalContext.Provider value={initialState}>
      {contextHolder}
      {children}
    </GlobalContext.Provider>
  );
};
