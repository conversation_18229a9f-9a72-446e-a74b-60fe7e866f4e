import { useLayoutEffect, useState } from 'react';

const useResize = () => {
  const [screen, setScreen] = useState<number>(window.innerWidth);

  useLayoutEffect(() => {
    const handleSize = () => {
      setScreen(window.innerWidth);
    };

    window.addEventListener('resize', handleSize);

    return () => {
      window.removeEventListener('resize', handleSize);
    };
  }, []);

  return screen;
};

export default useResize;
