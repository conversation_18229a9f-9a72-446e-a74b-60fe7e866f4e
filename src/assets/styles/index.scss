// @import url('https://fonts.googleapis.com/css?family=Open+Sans');
@font-face {
  font-family: 'UTM Avo';
  src: url('../fonts/UTM_Avo.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'UTM Avo';
  src: url('../fonts/UTM_AvoBold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'UTM Avo';
  src: url('../fonts/UTM_AvoItalic.ttf') format('truetype');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'UTM Avo';
  src: url('../fonts/UTM_AvoBold_Italic.ttf') format('truetype');
  font-weight: bold;
  font-style: italic;
}

:root {
  --primay-color: #F37421;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'UTM Avo', sans-serif;
  font-weight: normal;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  background: #f5f5f5;
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-wrap {
  white-space: nowrap;
}

.cursor-pointer {
  cursor: pointer;
}

// Text colors
.red-primary {
  color: #F6412E !important;
}

.green-primary {
  color: #0aad0a !important;
}

.green-dark {
  color: #008d0a !important;
}

.gray-darker {
  color: #616161 !important;
}

.orange-dark {
  color: #ff7f23 !important;
}

.text-title {
  color: #212121;
  font-weight: 500;
  font-size: 15px;
}

.text-count {
  color: #9e9e9e;
  font-weight: 400;
  font-size: 15px;
}

.icon-cover {
  display: flex;
  justify-content: center;
  gap: 8;
  color: #212121;
  border-radius: 2px;
  padding: 12px 20px;
  cursor: pointer;
}

.get-button {
  background: linear-gradient(135deg, #ffd78c, #ffb92d);
  background-color: #ffb92d;
  box-shadow: 0 1px 2px rgba(233, 59, 61, 0.2);
}

.get-button:hover,
.get-button:focus {
  background-color: #ffb92d !important;
  border-color: #ffb92d !important;
  color: #000000 !important;
}

.cart-button {
  background-color: #13A7B3 !important;
  border-color: #13A7B3 !important;
}

.cart-button:hover,
.cart-button:focus {
  background-color: #13A7B3 !important;
  border-color: #13A7B3 !important;
}

.cart-button:disabled {
  background-color: #EAE8E8 !important;
  border-color: #EAE8E8 !important;
}

.checkout-button {
  background-color: #F37421 !important;
  border-color: #F37421 !important;
}

.checkout-button:hover,
.checkout-button:focus {
  background-color: #F37421 !important;
  border-color: #F37421 !important;
}

.checkout-button:disabled {
  background-color: #EAE8E8 !important;
  border-color: #EAE8E8 !important;
}

.icon-hover:hover {
  background: linear-gradient(135deg, #ff0b2c, #ad0303);
  color: #ffffff;
  transition: all 0.3s ease;
}

h1 {
  font-family: 'UTM Avo';
  font-weight: bold;
}

em {
  font-family: 'UTM Avo';
  font-style: italic;
}

:where(.css-dev-only-do-not-override-qmu5gx).ant-menu-light.ant-menu-horizontal>.ant-menu-item::after {
  border-bottom: none !important;
}

:where(.css-dev-only-do-not-override-qmu5gx).ant-radio-wrapper {
  margin-inline-end: 0;
}

:where(.css-dev-only-do-not-override-qmu5gx).ant-typography-ellipsis-single-line {
  white-space: normal;
  overflow: visible;
  word-wrap: break-word;
}

:where(.css-dev-only-do-not-override-qmu5gx).ant-menu .ant-menu-item .anticon+span {
  margin-inline-start: 0
}

:where(.css-dev-only-do-not-override-qmu5gx).ant-list-grid .ant-col>.ant-list-item {
  margin-block-end: 0;
}

.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #F37421;
}

.ant-tabs-ink-bar {
  background-color: #F37421 !important;
}