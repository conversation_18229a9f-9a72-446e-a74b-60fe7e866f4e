import { PageResponse } from '@/dto/@common';
import { rootApiService } from './@common';
import {
  ComingSoonRes,
  LatestRevealRes,
  ListBuyedTicketReq,
  ListComingSoonReq,
  ListLatestRevealReq,
  ListProductsReq,
  ListUsersBuyedTicket,
  ProductDetailDto,
  ProductDetailReq,
  ProductsDto,
  SearchProductByNameReq,
} from '@/dto/product.dto';

const ENDPOINT = {
  LIST: '/api/publics/products/list'.trim(),
  PRODUCT_DETAIL: '/api/publics/products/detail'.trim(),
  LIST_LATEST_REVEAL: '/api/publics/products/list-latest-reveal'.trim(),
  LIST_COMING_SOON: '/api/publics/products/list-coming-soon'.trim(),
  LIST_USERS_BOYED_TICKET: '/api/publics/products/list-ticket-buyers'.trim(),
};

export class ProductsService {
  async list(params: ListProductsReq) {
    return rootApiService.get<PageResponse<ProductsDto>>(ENDPOINT.LIST, params);
  }
  
  async detail(params: ProductDetailReq) {
    return rootApiService.get<ProductDetailDto>(
      ENDPOINT.PRODUCT_DETAIL,
      params
    );
  }

  async listLatestReveal(params: ListLatestRevealReq) {
    return rootApiService.get<PageResponse<LatestRevealRes>>(
      ENDPOINT.LIST_LATEST_REVEAL,
      params
    );
  }

  async listComingSoon(params: ListComingSoonReq) {
    return rootApiService.get<PageResponse<ComingSoonRes>>(
      ENDPOINT.LIST_COMING_SOON,
      params
    );
  }

  async searchByName(query: SearchProductByNameReq) {
    return rootApiService.get<PageResponse<ProductsDto>>(ENDPOINT.LIST, query);
  }

  async buyedTicketList(params: ListBuyedTicketReq) {
    return rootApiService.get<ListUsersBuyedTicket>(
      ENDPOINT.LIST_USERS_BOYED_TICKET,
      params
    );
  }
}

export default new ProductsService();
