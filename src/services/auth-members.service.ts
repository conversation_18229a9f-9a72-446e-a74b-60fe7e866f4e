import {
  AuthMemberDto,
  ChangePasswordReq,
  ListNotificationsDto,
  ListNotificationsReq,
  ListTicketsReq,
  LoginMemberReq,
  MemberDto,
  MyAddressDto,
  MyAddressReq,
  RegisterMemberReq,
  TicketsDto,
  UpdateProfileReq,
} from '@/dto/auth-member.dto';
import { cdnApiService, rootApiService } from './@common';
import { PageResponse } from '@/dto/@common';

const ENDPOINT = {
  REGISTER: '/api/main/auth/register'.trim(),
  LOGIN: '/api/main/auth/login'.trim(),
  MEMBER_INFO: '/api/main/auth/member-info'.trim(),
  LIST_TICKET: '/api/main/auth/my-tickets'.trim(),
  LIST_NOTIFICATION: '/api/main/notifications/list'.trim(),
  ADDRESS: '/api/main/auth/my-addresses'.trim(),
  CHANGE_PASSWORD: '/api/main/auth/change-password'.trim(),
  UPDATE_PROFILE: '/api/main/auth/update-profile'.trim(),
  MARK_AS_READ: '/api/main/notifications/mark-as-read'.trim(),
};

const ENDPOINT_CDN = {
  UPLOAD_AVATAR: '/api/upload'.trim(),
};

export class MembersService {
  async memberInfo() {
    return rootApiService.get<MemberDto>(ENDPOINT.MEMBER_INFO);
  }

  async register(body: RegisterMemberReq) {
    return rootApiService.post<MemberDto>(ENDPOINT.REGISTER, body);
  }

  async login(body: LoginMemberReq) {
    return rootApiService.post<AuthMemberDto>(ENDPOINT.LOGIN, body);
  }

  async listTickets(query: ListTicketsReq) {
    return rootApiService.get<PageResponse<TicketsDto>>(
      ENDPOINT.LIST_TICKET,
      query
    );
  }

  async listNotification(query: ListNotificationsReq) {
    return rootApiService.get<PageResponse<ListNotificationsDto>>(
      ENDPOINT.LIST_NOTIFICATION,
      query
    );
  }

  async myAddress(query: MyAddressReq) {
    return rootApiService.get<PageResponse<MyAddressDto>>(
      ENDPOINT.ADDRESS,
      query
    );
  }

  async addMyAddress(body: MyAddressDto) {
    return rootApiService.post<PageResponse<MyAddressDto>>(
      ENDPOINT.ADDRESS,
      body
    );
  }

  async changePassword(body: ChangePasswordReq) {
    return rootApiService.put(ENDPOINT.CHANGE_PASSWORD, body);
  }

  async updateProfile(body: UpdateProfileReq) {
    return rootApiService.put(ENDPOINT.UPDATE_PROFILE, body);
  }

  async uploadAvatar(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    return cdnApiService.postForm(ENDPOINT_CDN.UPLOAD_AVATAR, formData);
  }

  async markAsRead(id: string[]) {
    return rootApiService.post(ENDPOINT.MARK_AS_READ, id);
  }
}

export default new MembersService();
