import {
  CreateOrderReq,
  IMyOrder,
  MyOrderReq,
  OrderDetailReq,
  VnpayCreatePaymentReq,
} from '@/dto/order.dto';
import { rootApiService } from './@common';
import { PageResponse } from '@/dto/@common';

const ENDPOINT = {
  CREATE_ORDER: '/api/main/orders/create'.trim(),
  VNPAY_CREATE_PAYMENT: '/api/main/payment/vnpay-create-payment'.trim(),
  ORDER_DETAIL: '/api/main/orders/detail'.trim(),
  ORDER_DETAIL_BY_PAYMENT_TRANSACTION:
    '/api/main/orders/detail-by-payment-transaction'.trim(),
  MY_ORDERS: '/api/main/orders/my-orders'.trim(),
};

export class OrdersService {
  async createOrder(body: CreateOrderReq) {
    return rootApiService.post(ENDPOINT.CREATE_ORDER, body);
  }

  async vnpayCreatePayment(body: VnpayCreatePaymentReq) {
    return rootApiService.post(ENDPOINT.VNPAY_CREATE_PAYMENT, body);
  }

  async detail(body: OrderDetailReq) {
    return rootApiService.get(ENDPOINT.ORDER_DETAIL, body);
  }

  async detailByPaymentTransaction(body: OrderDetailReq) {
    return rootApiService.get(
      ENDPOINT.ORDER_DETAIL_BY_PAYMENT_TRANSACTION,
      body
    );
  }

  async myOrders(params: MyOrderReq) {
    return rootApiService.get<PageResponse<IMyOrder>>(
      ENDPOINT.MY_ORDERS,
      params
    );
  }
}

export default new OrdersService();
