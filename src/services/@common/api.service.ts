import { ApiException } from '@/@core/dto';
import { createHttpClient, EMediaType } from '@/@core/network';
import { AxiosError, HttpStatusCode } from 'axios';

const handedError = (error?: any): ApiException => {
  if (!error) {
    return new ApiException('Unknown', HttpStatusCode.InternalServerError);
  }
  if (!error.isAxiosError) {
    if (error instanceof ApiException) {
      return error;
    }
    if (error.message) {
      return new ApiException(
        error.message,
        HttpStatusCode.InternalServerError
      );
    }
    return new ApiException('Unknown', HttpStatusCode.InternalServerError);
  }
  // eslint-disable-next-line prefer-const
  let { response, message = 'Unknown' } = error as AxiosError<any, any>;
  let type = 'DEFAULT';
  let businessCode = -1;
  if (response) {
    const { data = {}, status = HttpStatusCode.InternalServerError } = response;
    if (data.message) {
      message = data.message;
    }
    if (data.type) {
      type = data.type;
    }
    if (data.businessCode) {
      businessCode = data.businessCode;
    }
    return new ApiException(message, status, data, type, businessCode);
  }
  return new ApiException(message, HttpStatusCode.InternalServerError);
};

export const rootApiService = createHttpClient({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 2 * 60 * 1000,
  beforeRequest: (config) => {
    config.headers['Content-Type'] = EMediaType.APPLICATION_JSON;
    config.headers['Accept'] = EMediaType.APPLICATION_JSON;
    config.headers['Authorization'] = localStorage.getItem('token') || '';
    config.headers['x-lang'] = localStorage.getItem('lang') || '';
    return config;
  },
  handleError: (err) => {
    return handedError(err);
  },
  handleResponse: async (res) => {
    return res.data;
  },
});

export const cdnApiService = createHttpClient({
  baseURL: 'https://cdn.shoplucky.vn',
  timeout: 2 * 60 * 1000,
  beforeRequest: (config) => {
    return config;
  },
  handleError: (err) => {
    return handedError(err);
  },
  handleResponse: async (res) => {
    return res.data;
  },
});
