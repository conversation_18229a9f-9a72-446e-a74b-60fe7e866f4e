import { rootApiService } from './@common';
import { CartItem } from '@/types/CartItem';
import { PageResponse } from '@/dto/@common';

const ENDPOINT = {
  CART: '/api/main/cart'.trim(),
};

export class CartService {
  async getCart() {
    return rootApiService.get<PageResponse<CartItem>>(ENDPOINT.CART);
  }

  async updateCart(cartItems: CartItem[]) {
    return rootApiService.post(ENDPOINT.CART, { items: cartItems });
  }

  async removeItems(itemIds: string[]) {
    return rootApiService.delete(ENDPOINT.CART, { data: { itemIds } });
  }
}

export default new CartService();
