import { PageResponse } from '@/dto/@common';
import {
  CreateCategoryReq,
  CategoryDto,
  ListCategoryReq,
} from '@/dto/category.dto';
import { rootApiService } from './@common';

const ENDPOINT = {
  CREATE: '/api/publics/categories/create'.trim(),
  LIST: '/api/publics/categories/list'.trim(),
};

export class CategoryService {
  async create(body: CreateCategoryReq) {
    return rootApiService.post<CategoryDto>(ENDPOINT.CREATE, body);
  }

  async list(params: ListCategoryReq) {
    return rootApiService.get<PageResponse<CategoryDto>>(ENDPOINT.LIST, params);
  }
}

export default new CategoryService();
