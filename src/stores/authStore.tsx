import { MemberDto } from '@/dto/auth-member.dto';
import authMembersService from '@/services/auth-members.service';
import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  createContext,
  useContext,
} from 'react';
import { useNavigate } from 'react-router-dom';

interface IAuthStore {
  isLoading: boolean;
  member?: MemberDto | null;
  authenticate: () => Promise<void>;
  logout: () => void;
}

const AuthStoreContext = createContext<IAuthStore | undefined>(undefined);

const AuthStoreProvider = ({ children }: { children: React.ReactNode }) => {
  const [isLoading, setIsLoading] = useState(false);

  const [member, setMember] = useState<MemberDto | null>(null);

  const authenticate = useCallback(async () => {
    try {
      const res = await authMembersService.memberInfo();
      const memberInfo = {
        username: res?.username,
        phone: res?.phone,
        password: res?.password,
        email: res?.email,
        avatar: res?.avatar,
        status: res?.status,
        firstName: res?.firstName,
        lastName: res?.lastName,
        birthOfDate: res?.birthOfDate,
        personalId: res?.personalId,
        frontPersonalIdCardUrl: res?.frontPersonalIdCardUrl,
        backPersonalIdCardUrl: res?.backPersonalIdCardUrl,
        walletAddress: res?.walletAddress,
        balance: res?.balance,
        exactBalance: res?.exactBalance,
      };

      setMember(memberInfo);
    } catch (error) {
      setMember(null);
    }
  }, []);

  useEffect(() => {
    authenticate();
  }, [authenticate]);

  const logout = useCallback(() => {
    setMember(null);
    localStorage.removeItem('token');
  }, []);

  const values = useMemo(
    () => ({
      isLoading,
      member,
      authenticate,
      logout,
    }),
    [isLoading, member, authenticate, logout]
  );

  return (
    <AuthStoreContext.Provider value={values}>
      {children}
    </AuthStoreContext.Provider>
  );
};

const useAuthStore = () => {
  const context = useContext(AuthStoreContext);
  if (context === undefined) {
    throw new Error(
      'useAuthStore hook must be used with a AuthStoreContext component'
    );
  }
  return context;
};

export { AuthStoreProvider, useAuthStore };
