export namespace NSOrder {
  export enum EPaymentMethod {
    TICKET = 'TICKET',
    COD = 'COD',
    BANK_TRANSFER = 'BANK_TRANSFER',
  }

  export enum EStatus {
    PENDING_PAYMENT = 'PENDING_PAYMENT', // Chờ thanh toán (online)
    PENDING_CONFIRMATION = 'PENDING_CONFIRMATION', // Chờ xác nhận (COD)
    WAITING_FOR_SHIPMENT = 'WAITING_FOR_SHIPMENT', // Chờ giao hàng
    SHIPPING = 'SHIPPING', // Đang vận chuyển
    DELIVERED = 'DELIVERED', // Đã giao hàng
    COMPLETED = 'COMPLETED', // Hoàn tất (đã thanh toán & giao)
    CANCELED = 'CANCELED', // Đã huỷ
    RETURN_REQUESTED = 'RETURN_REQUESTED', // Y<PERSON>u cầu trả hàng
    RETURNED = 'RETURNED', // Đã trả hàng
    REFUNDED = 'REFUNDED', // Đã hoàn tiền
  }
  export const OrderStatusLabel: Record<EStatus, string> = {
    [EStatus.PENDING_PAYMENT]: 'Chờ thanh toán',
    [EStatus.PENDING_CONFIRMATION]: 'Chờ xác nhận',
    [EStatus.WAITING_FOR_SHIPMENT]: 'Chờ giao hàng',
    [EStatus.SHIPPING]: 'Vận chuyển',
    [EStatus.DELIVERED]: 'Đã giao hàng',
    [EStatus.COMPLETED]: 'Hoàn thành',
    [EStatus.CANCELED]: 'Đã hủy',
    [EStatus.RETURN_REQUESTED]: 'Yêu cầu trả hàng',
    [EStatus.RETURNED]: 'Trả hàng',
    [EStatus.REFUNDED]: 'Hoàn tiền',
  };
}
