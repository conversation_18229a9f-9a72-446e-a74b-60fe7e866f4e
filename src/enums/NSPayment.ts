export namespace NSPayment {
  export enum EPaymentType {
    WEB2 = 'WEB2',
    WEB3 = 'WEB3',
  }

  export enum EPaymentPurpose {
    DEPOSIT = 'DEPOSIT',
    PURCHASE = 'PURCHASE',
  }

  export enum EPaymentMethod {
    WEB3_TRON = 'WEB3_TRON',
    VNPAY = 'VNPAY',
  }
  export enum EStatus {
    PENDING = 'PENDING',
    SUCCESS = 'SUCCESS',
    FAILED = 'FAILED',
    REFUNDED = 'REFUNDED',
  }
  export enum ECurrency {
    VND = 'VND',
    USD = 'USD',
  }
}
