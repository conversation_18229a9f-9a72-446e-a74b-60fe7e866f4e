export enum ROUTES {
  HOME = '/',
  PRODUCT_LIST = '/products/:categoryId',
  PRODUCT_DETAILS = '/products/details/:id',
  PRODUCT_CART = '/products/cart',
  PRODUCT_ORDER = '/products/order',
  LOGIN = '/login',
  REGISTER = '/register',
  VOUCHER = '/voucher/:categoryId',
  ABOUTUS = '/about-us',
  FORGOT_PASSWORD = '/forgot-password',
  OTP = '/otp',
  UPDATE_PASSWORD = '/update-password',
}

export const NO_AUTH_ROUTES: string[] = [ROUTES.LOGIN, ROUTES.REGISTER, ROUTES.FORGOT_PASSWORD, ROUTES.OTP, ROUTES.UPDATE_PASSWORD];
