import { LANGUAGE } from '@/enums/Language';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import translationCN from './locales/cn.json';
import translationEN from './locales/en.json';
import translationVI from './locales/vi.json';

const resources = {
  vi: { translation: translationVI },
  en: { translation: translationEN },
  cn: { translation: translationCN },
};

const defaultLanguage = localStorage.getItem('lang') || LANGUAGE.EN

i18n.use(initReactI18next).init({
  resources,
  supportedLngs: [LANGUAGE.VI, LANGUAGE.EN, LANGUAGE.CN],
  fallbackLng: defaultLanguage,
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
