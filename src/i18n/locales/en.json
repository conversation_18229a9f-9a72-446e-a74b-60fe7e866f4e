{"welcome": "Welcome to Happy Shop", "loading": "Loading", "currency": "$", "see_more": "See more", "collapse": "Collapse", "product": {"description": "Product Description", "rating": "Rating", "product": "Product", "preparation_time": "Preparation Time", "response_time": "Response Time", "joined": "Joined", "chat_now": "Chat now", "view_shop": "View shop", "warehouse": "Warehouse", "product_reviews": "Product Reviews", "evaluate": "Evaluates", "other_products": "Other products of the store", "view_all": "View all", "also-like": "You may also like", "sold": "Sold {{sold}}", "standard": "Standard", "quantity": "Quantity", "delivery_schedule": "Delivery schedule", "next_delivery": "Next delivery: {{schedule}}", "purchase_price": "General purchase price", "free_shipping": "Free Shipping", "share": "Share", "liked": "Liked ({{liked}})", "add_to_cart": "Add to Cart", "buy_by_ticket": "Get involved now!", "purchases": "{{value}} purchases", "reviews": "{{value}} reviews", "offer_for": "Offer for {{text}}", "price": "Price: ", "remain": "Remaining", "ticket": "ticket", "ticketPrice": "Join now for ", "quantity_ticket": "Quantity ticket", "total_amount_ticket": "Total amount", "ticket_price": "Ticket price", "buy_now": "Buy now", "buy_with_voucher": "Buy Now with Voucher", "purchased_list": "Purchased list"}, "days": {"mon": {"short": "Mon", "full": "Monday"}, "tue": {"short": "<PERSON><PERSON>", "full": "Tuesday"}, "wed": {"short": "Wed", "full": "Wednesday"}, "thu": {"short": "<PERSON>hu", "full": "Thursday"}, "fri": {"short": "<PERSON><PERSON>", "full": "Friday"}, "sat": {"short": "Sat", "full": "Saturday"}, "sun": {"short": "Sun", "full": "Sunday"}}, "language": "EN", "languages": {"vietnam": "Vietnamese", "english": "English", "chinese": "Chinese"}, "header": {"home": "Home", "products": "Products", "aboutUs": "About us", "notice": "Notice", "cart": "<PERSON><PERSON>", "download": "Download App", "sellerCentre": "Seller Centre", "help": "Help", "order": "Order", "empty_notice": "No Notifications", "voucher": "Voucher", "personal_wallet": "Wallet", "download_app": "Download App", "support": "Support", "getNow": "Get Now", "checkNow": "Check Now"}, "crumbs": {"home": "Home", "categories": "Categories"}, "home": {"productCategories": "Product Categories", "recommend": "Recommend For You", "sold": "Sold", "addToCart": "Add To Cart", "all": "See all", "more": "Load More", "buy": "Buy a ticket", "remain": "Remaining", "ticket": "ticket", "product": "Product", "trend": "Search Trending", "coming": "Coming Soon", "latestReveal": "The Latest Reveal", "get": "Get ticket", "winner": "The winner is", "countdown": "Countdown", "searchPlaceholder": "Search for products", "trending": "Trending", "flashSale": "Flash Sale"}, "search": {"byCategories": "Categories", "price": "By Price", "orderBy": "Order By", "latest": "Latest", "lowPrice": "Low Price", "highPrice": "High Price", "bestSell": "Best Sell", "from": "From", "to": "To", "apply": "Apply"}, "cart": {"empty": "Cart is empty", "shop_now": "Shop now", "table": {"product": "Product", "price": "Price", "quantity": "Quantity", "amount": "Total amount", "products": "Products", "tickets": "Tickets"}, "choose_all": "Choose all", "all_products": " ({{count}} products)", "price": "Price: {{price}}", "temp_sum": "Provisional: ", "total": "Total amount: ", "order_info": "Order information", "total_temp": "Provisional ({{count}} products): ", "shipping": "Shipping: ", "payment": "Payment", "vat": "VAT included (if applicable)"}, "order": {"shipping_unit": "Select shipping unit", "note_to_seller": "Note to Sellers", "total_amount": "Total amount", "delivery_fee": "Delivery fee", "discount": "Discount", "total": "Total", "delivery_address": "Delivery address", "payment_method": "Payment method", "payment_methods": {"usdt": "Wallet USDT", "credit": "Wallet Credit", "bank": "Bank Transfer Payment (Bank transfer)", "cod": "Cash on Delivery (COD)"}, "delivery": {"title": "Delivery information", "receiver": "Receiver", "phone": "Phone number", "address": "Address", "enter_address": "Enter address"}, "payment_success": "Payment success", "payment_again": "Payment again", "failed": "Payment failed", "pending_payment": "Pending Payment", "all": "All", "awaiting_shipment": "Awaiting Shipment", "order_in_transit": "Order in Transit", "winning_voucher": "Winning Voucher", "non_winning_voucher": "Non-winning Voucher", "completed": "Completed", "change_address": "Change", "add_address": "Add address", "order_status": {"PENDING_PAYMENT": "Pending Payment", "PENDING_CONFIRMATION": "Pending Confirmation", "WAITING_FOR_SHIPMENT": "Waiting For Shipment", "SHIPPING": "Shipping", "DELIVERED": "Delivered", "COMPLETED": "Completed", "CANCELED": "Canceled", "RETURN_REQUESTED": "Return Requested", "RETURNED": "Returned", "REFUNDED": "Refunded"}}, "voucher": {"list_winners": "List of winners", "voucher_coming_soon": "Voucher coming soon"}, "button": {"delete": "Delete", "purchase": "Purchase", "apply": "Apply", "productsFilter": "Products filter"}, "footer": {"total": "Total amount: ", "total_ticket": "Total tickets amount: ", "support": {"title": "Customer Support", "hotline": "Customer care hotline: (+84)***********", "faq": "Frequently asked questions", "supportRequest": "Send support request", "orderGuide": "Order guide", "shippingMethod": "Shipping methods", "returnPolicy": "Return policy", "installmentGuide": "Installment guide", "importPolicy": "Imported goods policy", "emailSupport": "Customer support: <EMAIL>", "tradingConditions": "Information on general trading conditions", "happyShop": "Happy Shop Technology Trading Service Company Limited"}, "about": {"title": "About 'Shop Lucky'", "aboutUs": "About us", "careers": "Careers", "paymentPolicy": "Payment security policy", "privacyPolicy": "Personal information privacy policy", "complaintPolicy": "Complaint resolution policy", "terms": "Terms of use", "b2bSales": "B2B sales", "salesChannel": "Sales channels", "inspectionPolicy": "Inspection Policy", "warrantyPolicy": "Warranty policy"}, "payment": {"title": "Payment Methods", "methods": {"cash": "Cash", "momo": "<PERSON><PERSON>", "zaloPay": "Zalo <PERSON>", "vnpay": "VNPAY"}}, "social": {"title": "Connect with 'Shop Lucky'", "facebook": "Facebook", "youtube": "YouTube", "tiktok": "Tiktok", "zalo": "<PERSON><PERSON>"}, "info": {"officeAddress": "Office address: 365 <PERSON>ạm Ngũ Lão, District 1, Ho Chi Minh City, Viet Nam", "hotline": "Hotline: 0***********", "copyright": "© {{year}} - Copyright by Shoplucky Company - Shoplucky.vn", "issuer": "Issuer: Ho <PERSON> Minh City Department of Planning and Investment", "businessLicense": "Business Registration Certificate No. **********, issued by the Ho Chi Minh City Department of Planning and Investment on 09/01/2025."}}, "auth": {"title": "<PERSON><PERSON>", "registerTitle": "Register Account", "phone": "Phone number", "username": "Username", "usernameRequired": "Please enter your username", "password": "Password", "repassword": "Re-enter the password", "passwordRequired": "Please enter your password", "phoneRequired": "Please enter your phone", "forgot": "Forgot password?", "login": "<PERSON><PERSON>", "email": "Email", "firstName": "First name", "lastName": "Last name", "register": "Register", "noAccount": "Don't have an account?", "hasAccount": "Already have an account?", "loginSuccess": "<PERSON><PERSON> successfully", "loginFail": "<PERSON><PERSON> failed", "registerSuccess": "Register successfully", "registerFail": "Register failed", "logout": "Logout", "logoutSuccess": "Logout successfully", "logoutFail": "Logout failed", "profile": "Profile", "gender": "Gender", "address": "Address", "balance": "Account <PERSON><PERSON>", "infomation": "Infomation", "balance_usdt": "USDT Balance", "balance_credit": "Credit Balance", "general": "General Information", "notification": "Notification", "qr_code_info": "QR Code (Deposit USDT to TRON chain) ", "welcome": "Welcome back!", "privacyPolicy": "Privacy Policy", "email_required": "Please input email", "birthday": "Date of birthday", "birthday_required": "Please input birthday", "cskh": "Contact", "confirm": "Confirm", "verify_phone_number": "Phone Number Verification", "send_verification_code": "Verification code sent to phone number: {{phone}}", "enter_otp_code": "Enter OTP code", "noOtp": "Haven't received the OTP?", "reSend": "Resend", "enter_phone": "Enter phone number", "enter_new_password": "Enter new password", "change_password": "Change Password", "repasswordRequired": "Please re-enter the password", "confirm_email": "Confirm email", "top_up": "Add funds", "return": "Back", "edit": "Edit", "save": "Save", "cancel": "Cancel", "hello": "Hello!", "upload_avatar": "Upload avatar"}, "ticket": {"message": {"buy_ticket_success": {"title": "Successful Ticket Purchase", "content": "Successfully purchased {{qty}} ticket for {{amountBuy}} coins. Please check the ticket code in the “Voucher Information” section."}}, "title": "Tickets purchased", "ticket_number": "Ticket number", "ticket_code": "Ticket code: {{code}}"}, "profile": {"change_password": {"title": "Change password", "btn_update": "Update password", "old_password": "Old password", "new_password_required": "Please input new password", "new_password": "New password"}, "list_ticket": {"from_date": "From date", "to_date": "End date"}, "wallet_balance": "Account <PERSON><PERSON>", "order_information": "Order Information", "voucher_information": "Voucher Information", "deposit": "Deposit to VNPay", "deposit_tron": "QR Code (Deposit USDT to TRON chain)"}, "common": {"label": {"search": "Search"}, "payment_currency": "COIN", "deposit_coin": "<PERSON><PERSON><PERSON><PERSON>"}, "promo": {"personal_wallet": "Wallet"}, "error": {"unauthorized": "Please log in to your account.", "deposit_coin": "Insufficient account balance."}, "page": {"orderGuide": {"title.main": "Ordering Guide", "title.sub": "How do I place an order at Shop Lucky Viet Nam?", "intro": "Customers can place an order online at the Shoplucky.vn website by following these basic steps:", "step1.title": "Step 1: Search for a product", "step1.title.sub": "Customers can search for products in two ways:", "step1.method1": "Method 1: Type the product name into the search bar", "step1.method2": "Method 2: Search by product category", "step2.title": "Step 2: Add products to your shopping cart", "step2.details": "Once you have found the desired product, please check the product details:", "step2.check1": "Check product information: price, promotional details", "step2.check2": "Choose the desired quantity", "step2.check3": "Add the product to the shopping cart", "step2.check4": "Review the shopping cart and place an order", "step2.check5": "After having all the products in your cart, please use the DISCOUNT CODE (if available) and proceed to the next steps to place your order:", "step3.title": "Step 3: Adjust quantity and update the shopping cart", "step4.title": "Step 4: Click 'Proceed to checkout,' enter the discount code (if available) to start the order process.", "step5.title": "Step 5: Fill in the shipping address", "step6.title": "Step 6: Select a payment method and place your order", "step6.note1": "Shop Lucky Viet Nam offers delivery and cash-on-delivery payment for all orders nationwide. Currently, Shop Lucky Viet Nam accepts the following payment method:", "step6.note2": "Receive goods and pay in cash, with free shipping support", "step6.payment": "<PERSON><PERSON>ậ<PERSON> hàng và thanh toán tiền mặt, hỗ trợ phí ship", "step7.title": "Step 7: Check and complete your order", "step7.note": "Review the order details and then confirm the purchase. After placing the order, the system will display your order number.", "step8.title": "Step 8: Track the order status and receive the goods", "step8.note": "Your order will be delivered within 3-5 days, excluding Sundays and public holidays.", "thankyou": "Thank you for trusting and shopping at Shop Lucky Viet Nam. We wish you all the best!"}, "shipping": {"title": "SHIPPING METHODS", "intro": "Shop Lucky Viet Nam is committed to providing our customers with the fastest and most convenient shipping services. Below are the detailed information about our shipping policy:", "area.title": "1. Delivery Area", "area.content": "Nationwide: We deliver to all provinces across the country.", "methods.title": "2. Shipping Methods:", "methods.grab": "Grab: Suitable for orders requiring fast delivery within Ho Chi Minh City.", "methods.viettel": "Viettel Post: Suitable for orders outside the city with large weight or bulky size.", "time.title": "3. Delivery Time:", "time.content": "Free Shipping: We always offer free shipping promotions to provide the best shopping experience for our customers.", "fees.title": "4. Shipping Fees:", "fees.city": "In-city: Delivery time is 1-3 working days.", "fees.outside": "Out-of-city: Delivery time is 3-5 working days.", "process.title": "5. Delivery Process:", "process.confirm": "Order Confirmation: After placing the order successfully, you will receive a confirmation email or SMS.", "process.pack": "Packaging: We will carefully package the product to ensure it reaches you in perfect condition.", "process.deliver": "Delivery: The shipping company will deliver the product to the address you provided.", "process.receive": "Receiving Goods: Upon receiving the goods, please check the product carefully before payment.", "payment.title": "6. Payment Methods:", "payment.cod": "Cash on Delivery (COD): You pay directly to the delivery person.", "note.title": "Note:", "note.content1": "Delivery time may change depending on external factors such as weather, delivery address, public holidays, etc.", "note.content2": "In case of delays or service issues, we will notify you promptly and offer an opportunity to cancel the order if desired.", "note.contact": "For further details, please contact us.", "closing": "Shop Lucky Viet Nam is committed to providing the best shopping experience for you!"}, "returnPolicy": {"title": "RETURN AND <PERSON><PERSON><PERSON><PERSON><PERSON> POLICY", "conditions.title": "1. Conditions for Returns and Exchanges", "conditions.faulty": "- Faulty products due to the manufacturer: The product is defective in quality, does not function as described, or has technical issues.", "conditions.original": "- Products in original condition: The product must have its original tags, packaging, and accessories intact.", "conditions.video": "- Video confirmation: Customers must provide a clear video showing the defective product as evidence. The video must clearly show the product details, defects, and the date it was filmed.", "time.title": "2. Return and Exchange Time", "time.content": "Within 30 days from the receipt of the goods, customers have the right to request a return or exchange if the product is faulty due to the manufacturer.", "process.title": "3. Return and Exchange Process", "process.notify": "- Notification: Customers contact customer service to notify them of their intention to return or exchange the product.", "process.info": "- Provide Information: Customers provide complete information about the order, defective product, and send the confirmation video.", "process.verify": "- Verification: Customer service will verify the information and video provided by the customer.", "process.resolve": "- Return or Exchange: If the product meets the return/exchange conditions, we will proceed to exchange it for a new equivalent product or refund the customer.", "note.title": "Note:", "note.shipping": "- Shipping Fees: The shipping cost for returns and exchanges will be borne by Shop Lucky Viet Nam.", "note.time": "- Processing Time: The time to process a return or exchange request may vary depending on the specific case.", "note.refund": "- Refund Method: We will refund to the customer's bank account or e-wallet.", "videoExample.title": "Example of video confirmation content:", "videoExample": "The video should clearly show all angles of the product, focusing on the defective part. Customers should clearly mention the product name, order number, and a detailed description of the defect."}}}