{"welcome": "Chào mừng đến với Happy Shop", "loading": "<PERSON><PERSON> t<PERSON>", "currency": "đ", "see_more": "<PERSON><PERSON>", "collapse": "<PERSON><PERSON>", "product": {"description": "<PERSON><PERSON> t<PERSON> sản phẩm", "rating": "Đánh giá", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "preparation_time": "<PERSON><PERSON><PERSON><PERSON> gian ch<PERSON>n bị", "response_time": "<PERSON><PERSON><PERSON><PERSON> gian ph<PERSON>n hồi", "joined": "Đ<PERSON> tham gia", "chat_now": "<PERSON>t ngay", "view_shop": "Xem shop", "warehouse": "<PERSON><PERSON>", "product_reviews": "<PERSON><PERSON><PERSON> gi<PERSON> sản phẩm", "evaluate": "Đánh giá", "other_products": "<PERSON><PERSON><PERSON> ph<PERSON>m kh<PERSON>c của cửa hàng", "view_all": "<PERSON><PERSON> t<PERSON>t cả", "also-like": "<PERSON><PERSON> thể bạn cũng thích", "sold": "<PERSON><PERSON> bán {{sold}}", "standard": "<PERSON><PERSON><PERSON><PERSON>", "quantity": "Số lượng", "delivery_schedule": "<PERSON><PERSON><PERSON> giao h<PERSON>ng", "next_delivery": "<PERSON><PERSON><PERSON> giao kế tiếp: {{schedule}}", "purchase_price": "<PERSON><PERSON><PERSON> mua chung", "free_shipping": "<PERSON><PERSON><PERSON> phí vận chuyển", "share": "<PERSON><PERSON> sẻ", "liked": "<PERSON><PERSON> thích ({{liked}})", "add_to_cart": "Thêm vào giỏ hàng", "buy_by_ticket": "<PERSON>ham gia ngay", "purchases": "{{value}} l<PERSON><PERSON><PERSON> mua", "reviews": "{{value}} đ<PERSON>h giá", "offer_for": "Ưu đãi cho {{text}}", "price": "Giá: ", "remain": "<PERSON>òn lại", "ticket": "ticket", "ticketPrice": "Tham gia ngay với giá ", "ticket_price": "Giá vé", "quantity_ticket": "Số vé", "total_amount_ticket": "<PERSON><PERSON><PERSON> tiền", "buy_now": "<PERSON><PERSON> ngay", "buy_with_voucher": "<PERSON><PERSON> ngay v<PERSON><PERSON> Voucher", "purchased_list": "<PERSON><PERSON> s<PERSON>ch đã mua"}, "days": {"mon": {"short": "T2", "full": "Thứ 2"}, "tue": {"short": "T3", "full": "Thứ 3"}, "wed": {"short": "T4", "full": "Thứ 4"}, "thu": {"short": "T5", "full": "Thứ 5"}, "fri": {"short": "T6", "full": "Thứ 6"}, "sat": {"short": "T7", "full": "Thứ 7"}, "sun": {"short": "CN", "full": "Chủ Nhậ<PERSON>"}}, "language": "VN", "languages": {"vietnam": "Tiếng <PERSON>", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "<PERSON><PERSON><PERSON>ng <PERSON>"}, "header": {"home": "Trang chủ", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "aboutUs": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "notice": "<PERSON><PERSON><PERSON><PERSON> báo", "cart": "Giỏ hàng", "download": "<PERSON><PERSON><PERSON> dụng", "sellerCentre": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "help": "<PERSON><PERSON><PERSON> g<PERSON>", "order": "<PERSON><PERSON> toán", "empty_notice": "<PERSON><PERSON><PERSON> có thông báo", "voucher": "Voucher", "personal_wallet": "Ví cá nhân", "download_app": "<PERSON><PERSON><PERSON> dụng", "support": "Hỗ trợ", "getNow": "<PERSON><PERSON><PERSON><PERSON> ngay", "checkNow": "<PERSON><PERSON><PERSON> tra ngay"}, "crumbs": {"home": "Trang chủ", "categories": "<PERSON><PERSON>"}, "home": {"productCategories": "<PERSON><PERSON> m<PERSON><PERSON> sản ph<PERSON>m", "recommend": "Gợi ý cho bạn", "sold": "<PERSON><PERSON> bán", "addToCart": "Thêm vào giỏ hàng", "all": "<PERSON><PERSON> t<PERSON>t cả", "more": "<PERSON><PERSON>", "buy": "<PERSON><PERSON> vé ngay", "remain": "<PERSON>òn lại", "ticket": "vé", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "trend": "<PERSON> h<PERSON> tìm kiếm", "coming": "<PERSON><PERSON><PERSON> ra", "latestReveal": "<PERSON><PERSON><PERSON> quả mới nhất", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON>m sản phẩm", "get": "Mua vé", "winner": "<PERSON><PERSON><PERSON><PERSON> chiến thắng", "countdown": "<PERSON><PERSON> đếm giờ", "trending": "Trending", "flashSale": "Flash Sale"}, "search": {"byCategories": "<PERSON><PERSON> m<PERSON><PERSON> sản ph<PERSON>m", "price": "<PERSON>", "orderBy": "<PERSON><PERSON><PERSON> xếp theo", "latest": "<PERSON><PERSON><PERSON>", "lowPrice": "<PERSON><PERSON><PERSON> thấp", "highPrice": "<PERSON><PERSON><PERSON> cao", "bestSell": "<PERSON><PERSON>", "from": "Từ", "to": "<PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON>"}, "cart": {"empty": "Giỏ hàng đang trống", "shop_now": "<PERSON><PERSON> s<PERSON> ngay", "table": {"product": "<PERSON><PERSON><PERSON> p<PERSON>m", "price": "Đơn giá", "quantity": "Số lượng", "amount": "<PERSON><PERSON><PERSON><PERSON> tiền", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "tickets": "Vé"}, "choose_all": "<PERSON><PERSON><PERSON> tất cả", "all_products": " ({{count}} sản phẩm)", "price": "Giá: {{price}}", "temp_sum": "<PERSON><PERSON><PERSON> t<PERSON>: ", "total": "Tổng cộng: ", "order_info": "<PERSON><PERSON><PERSON><PERSON> tin đơn hàng", "total_temp": "Tạ<PERSON> t<PERSON>h ({{count}} sản phẩm): ", "shipping": "<PERSON><PERSON> vận chuyển: ", "payment": "<PERSON><PERSON> toán", "vat": "<PERSON><PERSON> bao gồm VAT (nếu có)"}, "order": {"shipping_unit": "<PERSON><PERSON><PERSON> đơn vị vận chuyển", "note_to_seller": "<PERSON><PERSON><PERSON> cho ng<PERSON>ời bán", "total_amount": "<PERSON><PERSON><PERSON> tiền hàng", "delivery_fee": "<PERSON><PERSON> vận chuy<PERSON>n", "discount": "G<PERSON>ảm giá", "total": "Tổng phí", "delivery_address": "Địa chỉ nhận hàng", "payment_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "payment_methods": {"usdt": "Ví USDT", "credit": "Ví Credit", "bank": "<PERSON><PERSON> <PERSON><PERSON> qua <PERSON> hà<PERSON> (chuyển k<PERSON>ản)", "cod": "<PERSON><PERSON> <PERSON><PERSON> khi nh<PERSON>n hàng(COD)"}, "delivery": {"title": "<PERSON>h<PERSON><PERSON> tin nhận hàng", "receiver": "<PERSON><PERSON><PERSON><PERSON>n", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "enter_address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ"}, "payment_success": "<PERSON><PERSON> to<PERSON> thành công", "failed": "<PERSON><PERSON> to<PERSON> thất bại", "payment_again": "<PERSON><PERSON> lại", "pending_payment": "Chờ thanh toán", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "awaiting_shipment": "<PERSON><PERSON> giao hàng", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "order_in_transit": "<PERSON><PERSON><PERSON> hàng đang vận chuyển", "winning_voucher": "Voucher trúng thưởng", "non_winning_voucher": "Voucher không trúng thưởng", "change_address": "<PERSON><PERSON> đ<PERSON>i", "add_address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "order_status": {"PENDING_PAYMENT": "Chờ thanh toán", "PENDING_CONFIRMATION": "<PERSON>ờ x<PERSON>c n<PERSON>n", "WAITING_FOR_SHIPMENT": "<PERSON><PERSON> giao hàng", "SHIPPING": "<PERSON><PERSON> giao hàng", "DELIVERED": "Đ<PERSON> giao hàng", "COMPLETED": "<PERSON><PERSON><PERSON> th<PERSON>", "CANCELED": "<PERSON><PERSON> hủy", "RETURN_REQUESTED": "<PERSON><PERSON><PERSON> c<PERSON>u trả lại", "RETURNED": "Đã trả lại", "REFUNDED": "<PERSON><PERSON> hoàn tiền"}}, "voucher": {"list_winners": "<PERSON><PERSON> s<PERSON>ch trúng thưởng", "voucher_coming_soon": "Voucher sắp quay"}, "button": {"delete": "Xoá", "purchase": "<PERSON><PERSON>", "apply": "<PERSON><PERSON>", "productsFilter": "<PERSON><PERSON> l<PERSON> sản phẩm"}, "footer": {"total": "<PERSON><PERSON><PERSON><PERSON> tiền: ", "total_ticket": "<PERSON><PERSON><PERSON><PERSON> tiền vé: ", "support": {"title": "Hỗ trợ khách hàng", "hotline": "Hotline chăm sóc kh<PERSON>ch hàng: 0909 689 026", "faq": "<PERSON><PERSON><PERSON> câu hỏi thường gặp", "supportRequest": "<PERSON><PERSON><PERSON> yêu cầu hỗ trợ", "orderGuide": "Hướng dẫn đặt hàng", "shippingMethod": "<PERSON><PERSON><PERSON><PERSON> thức vận chuy<PERSON>n", "returnPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch đổi trả", "installmentGuide": "Hướng dẫn trả góp", "importPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch hàng nh<PERSON> kh<PERSON>u", "emailSupport": "Hỗ trợ khách hàng: <EMAIL>", "tradingConditions": "<PERSON>h<PERSON>ng tin về điều kiện giao dịch chung", "happyShop": "Công ty TNHH Dị<PERSON>ương Mại Công Nghệ Happy Shop"}, "about": {"title": "Về Shop Lucky", "aboutUs": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "careers": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "paymentPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch b<PERSON>o mật thanh toán", "privacyPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch b<PERSON>o mật thông tin cá nhân", "complaintPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch g<PERSON><PERSON>i quyết khi<PERSON>u nại", "terms": "<PERSON><PERSON><PERSON><PERSON>n sử dụng", "b2bSales": "<PERSON><PERSON> h<PERSON>ng do<PERSON>h ng<PERSON>", "salesChannel": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "inspectionPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch ki<PERSON> hàng", "warrantyPolicy": "<PERSON><PERSON><PERSON> s<PERSON> b<PERSON><PERSON> h<PERSON>nh"}, "payment": {"title": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "methods": {"cash": "Tiền mặt", "momo": "<PERSON><PERSON>", "zaloPay": "Zalo <PERSON>", "vnpay": "VNPAY"}}, "social": {"title": "<PERSON><PERSON>t n<PERSON>i với Shop Lucky", "facebook": "Facebook", "youtube": "YouTube", "tiktok": "Tiktok", "zalo": "<PERSON><PERSON>"}, "info": {"officeAddress": "Địa chỉ văn phòng: 365 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "hotline": "Hotline: 0909 689 026", "copyright": "© {{year}} - <PERSON><PERSON><PERSON> quyền của Shoplucky - Shoplucky.vn", "issuer": "Nơi cấp: Sở kế hoạch và đầu tư TP. <PERSON><PERSON><PERSON> - Phòng đăng ký kinh doanh", "businessLicense": "<PERSON><PERSON><PERSON><PERSON> chứng nhận đăng ký kinh doanh số ********** do Sở kế hoạch và Đầu tư Thành phố <PERSON><PERSON> <PERSON><PERSON> cấp ngày 09/01/2025."}}, "auth": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> tà<PERSON>", "registerTitle": "<PERSON><PERSON><PERSON> ký tài k<PERSON>n", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "username": "<PERSON><PERSON><PERSON> tà<PERSON>", "usernameRequired": "<PERSON><PERSON> lòng nh<PERSON>p tà<PERSON>", "password": "<PERSON><PERSON><PERSON>", "repassword": "<PERSON><PERSON><PERSON><PERSON> lại mật kh<PERSON>u", "passwordRequired": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "repasswordRequired": "<PERSON><PERSON> lòng nhập lại mật khẩu", "phoneRequired": "<PERSON><PERSON> lòng nhập số điện thoại", "forgot": "<PERSON>uên mật khẩu?", "login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON> ký", "email": "Email", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "noAccount": "Bạn chưa có tài khoản?", "hasAccount": "Bạn đã có tài k<PERSON>n?", "loginSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "loginFail": "<PERSON><PERSON><PERSON> k<PERSON>n hoặc mật khẩu không đúng", "registerSuccess": "<PERSON><PERSON><PERSON> ký thành công", "registerFail": "<PERSON><PERSON><PERSON> ký thất bại", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "logoutSuccess": "<PERSON><PERSON><PERSON> xuất thành công", "logoutFail": "<PERSON><PERSON><PERSON> xuất thất bại", "profile": "<PERSON><PERSON> sơ", "infomation": "Thông tin cá nhân", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "address": "Địa chỉ", "balance": "Số dư tài <PERSON>", "balance_usdt": "Số dư USDT", "balance_credit": "Số dư credit", "general": "Th<PERSON>ng tin chung", "notification": "<PERSON><PERSON><PERSON><PERSON> báo", "qr_code_info": "Mã QR (Gửi USDT vào TRON)", "welcome": "Chào mừng bạn trở lại!", "privacyPolicy": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> b<PERSON><PERSON> mật", "email_required": "<PERSON><PERSON> lòng nhập email", "birthday": "<PERSON><PERSON><PERSON>", "birthday_required": "<PERSON><PERSON> lòng nh<PERSON>p ngày sinh", "cskh": "CSKH", "confirm": "<PERSON><PERSON><PERSON>", "verify_phone_number": "<PERSON><PERSON><PERSON> thực số điện thoại", "send_verification_code": "<PERSON><PERSON> xác thực đ<PERSON>c gửi qua số điện thoại: {{phone}}", "enter_otp_code": "<PERSON>hậ<PERSON> mã OTP", "enter_phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enter_new_password": "<PERSON><PERSON><PERSON><PERSON> mật khẩu mới ở dưới", "noOtp": "Bạn chưa nhận đư<PERSON>c mã OTP?", "reSend": "<PERSON><PERSON><PERSON> lại ngay", "change_password": "<PERSON>hay đ<PERSON>i mật kh<PERSON>u", "confirm_email": "<PERSON><PERSON><PERSON> email", "top_up": "<PERSON><PERSON><PERSON>ê<PERSON>", "return": "Quay lại", "edit": "Chỉnh sửa", "save": "<PERSON><PERSON><PERSON>", "cancel": "Huỷ", "hello": "Xin chào!", "upload_avatar": "<PERSON><PERSON><PERSON>nh đại di<PERSON>n"}, "ticket": {"title": "<PERSON><PERSON> đã mua", "message": {"buy_ticket_success": {"content": "<PERSON><PERSON> thành công {{qty}} vé với số {{amountBuy}} xu, kiểm tra mã vé trong mục “Thông tin Voucher”", "title": "<PERSON><PERSON> vé thành công"}}, "ticket_number": "Số vé", "ticket_code": "Mã vé: {{code}}"}, "profile": {"change_password": {"title": "<PERSON>hay đ<PERSON>i mật kh<PERSON>u", "btn_update": "<PERSON><PERSON><PERSON> nh<PERSON>t mật kh<PERSON>u", "old_password": "<PERSON><PERSON><PERSON> cũ", "old_password_required": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u cũ", "new_password_required": "<PERSON><PERSON> lòng nhập mật khẩu mới", "new_password": "<PERSON><PERSON><PERSON> mới"}, "list_ticket": {"from_date": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "to_date": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "wallet_balance": "Số dư tài <PERSON>", "order_information": "<PERSON><PERSON><PERSON><PERSON> tin đơn hàng", "voucher_information": "Thông tin voucher", "deposit": "<PERSON><PERSON><PERSON> tiền qua VNPay", "deposit_tron": "Mã QR (Nạp USDT vào chuỗi TRON)"}, "common": {"label": {"search": "<PERSON><PERSON><PERSON>"}, "payment_currency": "<PERSON>", "deposit_coin": "<PERSON><PERSON><PERSON> xu"}, "promo": {"personal_wallet": "Ví cá nhân"}, "error": {"unauthorized": "<PERSON><PERSON> lòng đăng nhập tà<PERSON>.", "deposit_coin": "S<PERSON> dư tài k<PERSON>n không đủ."}, "page": {"orderGuide": {"title.main": "Hướng dẫn mua hàng", "title.sub": "<PERSON><PERSON><PERSON> thế nào để tôi đặt hàng ở Shop Lucky Viet Nam?", "intro": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng có thể đặt hàng trực tuyến ở website Shoplucky.vn qua những bước đặt hàng cơ bản sau:", "step1.title": "Bước 1: <PERSON><PERSON><PERSON> kiếm sản phẩm", "step1.title.sub": "<PERSON><PERSON><PERSON> khách có thể tìm kiếm sản phẩm theo 2 cách sau:", "step1.method1": "Cách 1: <PERSON><PERSON> tên sản phẩm vào thanh tìm kiếm", "step1.method2": "Cách 2: <PERSON><PERSON><PERSON> theo danh mục sản phẩm", "step2.title": "Bước 2: <PERSON><PERSON><PERSON><PERSON> sản phẩm vào giỏ hàng", "step2.details": "<PERSON>hi đã tìm đ<PERSON><PERSON><PERSON> sản phẩm mong muốn, quý khách vui lòng kiểm tra thông tin chi tiết của sản phẩm:", "step2.check1": "<PERSON><PERSON>m tra thông tin sản phẩm: gi<PERSON>, thông tin khuyến mãi", "step2.check2": "<PERSON><PERSON><PERSON> số lượng mong muốn", "step2.check3": "<PERSON>h<PERSON><PERSON> sản phẩm vào giỏ hàng", "step2.check4": "<PERSON><PERSON><PERSON> tra giỏ hàng và đặt hàng", "step2.check5": "Sử dụng mã giảm giá (nếu có) và tiếp tục các bước sau để đặt hàng:", "step3.title": "Bước 3: <PERSON><PERSON><PERSON><PERSON> chỉnh số lượng và cập nhật giỏ hàng", "step4.title": "Bước 4: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> hành thanh toán” , nhập mã giảm giá (nếu có) để bắt đầu đặt hàng.", "step5.title": "Bước 5: <PERSON><PERSON><PERSON><PERSON> địa chỉ giao hàng", "step6.title": "Bước 6: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> thức thanh toán và đặt hàng", "step6.note1": "Shop Lucky Viet Nam hỗ trợ giao hàng và thanh toán tận nơi cho tất cả các đơn hàng trên toàn quốc.", "step6.note2": "Hiện tại Shop Lucky Viet Nam đang sử dụng ph<PERSON><PERSON><PERSON> thức thanh toán sau:", "step6.payment": "<PERSON><PERSON>ậ<PERSON> hàng và thanh toán tiền mặt, hỗ trợ phí ship", "step7.title": "Bước 7: <PERSON><PERSON><PERSON> tra và hoàn thành đặt hàng", "step7.note": "Bạn kiểm tra lại đơn hàng dự định đặt, tiế<PERSON> đ<PERSON> đặt mua. <PERSON><PERSON> <PERSON>hi đặt mua, hệ thống sẽ hiện thị mã đơn hàng của bạn.", "step8.title": "Bước 8: <PERSON> tình trạng đơn và nhận hàng", "step8.note": "Đơn hàng của bạn sẽ được giao trung bình sau 3-5 ngày. Không kể ngày chủ nhật và các ngày lễ.", "thankyou": "Cảm ơn quý khách hàng tin tưởng và mua hàng tại Shop Lucky Viet Nam. Chúc quý khách vạn sự như ý!"}, "shipping": {"title": "PHƯƠNG THỨC VẬN CHUYỂN", "intro": "Shop Lucky Viet Nam cam kết mang đến cho quý khách hàng dịch vụ vận chuyển nhanh chóng và tiện lợi nhất. Dưới đây là những thông tin chi tiết về chính sách vận chuyển của chúng tôi:", "area.title": "1. <PERSON><PERSON><PERSON> vi giao hàng", "area.content": "Toàn Quốc: <PERSON><PERSON><PERSON> tôi giao hàng đến tất cả các tỉnh thành trên toàn quốc.", "methods.title": "2. <PERSON><PERSON><PERSON><PERSON> thức vận chuyể<PERSON>:", "methods.grab": "Grab: <PERSON><PERSON> hợp cho đơn hàng có yêu cầu giao hàng nhanh trong thành phố <PERSON><PERSON>", "methods.viettel": "Viettel post: <PERSON><PERSON> hợp cho đơn hàng ngoại thành có trọng lượng lớn hoặc kích thước cồng kềnh.", "time.title": "3. <PERSON><PERSON><PERSON><PERSON> gian giao hàng:", "time.content": "Miễn phí vận chuyển: <PERSON><PERSON><PERSON> tôi luôn khuyến mãi miễn phí vận chuyển để quý khách có trải nghiệm mua hàng tốt nhất.", "fees.title": "4. <PERSON><PERSON> vận chuy<PERSON>:", "fees.city": "<PERSON>ội thành: <PERSON><PERSON><PERSON><PERSON> gian giao 1-3 ng<PERSON><PERSON> làm việc", "fees.outside": "<PERSON><PERSON><PERSON><PERSON> thành: <PERSON><PERSON><PERSON><PERSON> gian giao 3-5 ng<PERSON><PERSON> làm vi<PERSON>c", "process.title": "5. <PERSON><PERSON> tr<PERSON><PERSON> giao hàng:", "process.confirm": "<PERSON><PERSON><PERSON> nhận đơn hàng: <PERSON><PERSON> khi đặt hàng thành công, quý khách sẽ nhận được email hoặc tin nhắn xác nhận đơn hàng.", "process.pack": "<PERSON>óng gói hàng hóa: Chúng tôi sẽ tiến hành đóng gói sản phẩm cẩn thận và đảm bảo hàng hóa đến tay quý khách trong tình trạng nguyên vẹn.", "process.deliver": "Giao hàng: Đơn vị vận chuyển sẽ giao hàng đến địa chỉ quý khách đã cung cấp.", "process.receive": "Nhận hàng: <PERSON><PERSON> nhận hàng, quý kh<PERSON>ch vui lòng kiểm tra kỹ sản phẩm trước khi thanh toán.", "payment.title": "6. <PERSON><PERSON>:", "payment.cod": "<PERSON><PERSON> toán khi nhận hàng (COD): <PERSON><PERSON><PERSON> kh<PERSON>ch thanh toán trực tiếp cho nhân viên giao hàng.", "note.title": "<PERSON><PERSON><PERSON> ý:", "note.content1": "Thời gian giao hàng có thể thay đổi tùy thuộc vào các yếu tố khách quan như thời tiết, đ<PERSON><PERSON> chỉ giao hàng, ng<PERSON><PERSON> l<PERSON>…", "note.content2": "Tr<PERSON><PERSON><PERSON> hợp phát sinh chậm trễ trong việc giao hàng hoặc cung ứng dịch vụ, chúng tôi sẽ có thông tin kịp thời cho khách hàng và tạo cơ hội để khách hàng có thể hủy đơn nếu muốn.", "note.contact": "<PERSON><PERSON> đ<PERSON> tư vấn chi tiế<PERSON> hơn, quý kh<PERSON>ch vui lòng liên hệ với chúng tôi.", "closing": "Shop Lucky Viet Nam cam kết mang đến cho quý khách hàng trải nghiệm mua sắm tuyệt vời nhất!"}, "returnPolicy": {"title": "CHÍNH SÁCH ĐỔI TRẢ", "conditions.title": "1. <PERSON><PERSON><PERSON><PERSON> kiện đổi trả", "conditions.faulty": "<PERSON><PERSON><PERSON> hóa bị lỗi do nhà sản xuất: <PERSON><PERSON><PERSON> phẩm bị lỗi về chất lượng, không hoạt động đúng như mô tả hoặc có lỗi kỹ thuật.", "conditions.original": "<PERSON><PERSON><PERSON> hóa còn nguyên vẹn: <PERSON><PERSON>n phẩm phải còn nguyên tem mác, bao bì, phụ kiện đi kèm.", "conditions.video": "<PERSON>ó video xác nhận: <PERSON><PERSON><PERSON><PERSON> hàng cần cung cấp video quay rõ ràng sản phẩm bị lỗi để làm bằng chứng. Video phải quay rõ các chi tiết về sản phẩm, lỗi hỏng và ngày quay.", "time.title": "2. <PERSON><PERSON><PERSON><PERSON> gian đổi trả", "time.content": "Trong vòng 30 ngày kể từ ngày nhận hàng, kh<PERSON><PERSON> hàng có quyền yêu cầu đổi trả sản phẩm nếu sản phẩm đó bị lỗi do nhà sản xuất.", "process.title": "3. <PERSON><PERSON> tr<PERSON>nh đổi trả", "process.notify": "Thông báo: <PERSON><PERSON><PERSON><PERSON> hàng liên hệ với bộ phận chăm sóc khách hàng để thông báo về việc muốn đổi trả sản phẩm.", "process.info": "<PERSON>ung cấp thông tin: <PERSON><PERSON><PERSON><PERSON> hàng cung cấp đ<PERSON><PERSON> đủ thông tin về đơn hàng, s<PERSON><PERSON> phẩm bị lỗi và gửi video xác nhận.", "process.verify": "Kiểm tra: <PERSON><PERSON> phận chăm sóc khách hàng sẽ tiến hành kiểm tra thông tin và video mà khách hàng cung cấp.", "process.resolve": "Đổi trả: <PERSON><PERSON><PERSON> sản phẩm đáp ứng đủ điều kiện đổi trả, chúng tôi sẽ tiến hành đổi sản phẩm mới tương đương hoặc hoàn tiền cho khách hàng.", "note.title": "<PERSON><PERSON><PERSON> ý:", "note.shipping": "<PERSON>í vận chuyển: Chi phí vận chuyển đổi trả sẽ do Shop Lucky Viet Nam chịu trách nhiệm.", "note.time": "Thời gian xử lý: Thời gian xử lý yêu cầu đổi trả có thể thay đổi tùy thuộc vào từng trường hợp cụ thể.", "note.refund": "<PERSON><PERSON><PERSON> thức hoàn tiền: <PERSON><PERSON><PERSON> tôi sẽ hoàn tiền vào tài khoản ngân hàng hoặc ví điện tử của khách hàng.", "videoExample.title": "<PERSON><PERSON> dụ về nội dung video xác nhận:", "videoExample": "Video cần quay rõ các góc cạnh của sản phẩm, tập trung vào phần bị lỗi. <PERSON><PERSON><PERSON><PERSON> hàng nên nói rõ tên sản phẩm, mã đơn hàng và mô tả chi tiết về lỗi hỏng."}}}