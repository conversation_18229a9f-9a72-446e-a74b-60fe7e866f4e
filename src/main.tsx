import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';
import './assets/styles/index.scss';
import '@ant-design/v5-patch-for-react-19';
import './i18n';

import { GlobalProvider } from '@/hooks/useGlobalContext.hook';
import { App, ConfigProvider } from 'antd';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { radius, tokenColor } from './constants/themeColor';
import Routes from './routes';
import { AuthStoreProvider } from "./stores/authStore";

const root = createRoot(document.getElementById('root') as HTMLElement);

root.render(
  <React.StrictMode>

    <ConfigProvider
      theme={{
        token: {
          fontFamily: [`"UTM Avo"`, 'sans-serif'].join(','),
          colorPrimary: tokenColor,
          borderRadius: radius,
        },
      }}
    >
      <App>
        <GlobalProvider>
          <AuthStoreProvider>
            <Routes />
          </AuthStoreProvider>
        </GlobalProvider>
      </App>
    </ConfigProvider>

  </React.StrictMode>
);
