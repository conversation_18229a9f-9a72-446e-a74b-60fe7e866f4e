import { Card, Row, Col, Typography } from 'antd';
import { ProductDetailDto } from '@/dto/product.dto';
import { FC } from 'react';

const { Text } = Typography;

const ProductItemVertical: FC<{ product: ProductDetailDto }> = ({
  product,
}) => {
  return (
    <Card
      style={{
        backgroundColor: '#f5f5f5',
        borderRadius: '10px',
        padding: '15px',
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <Row gutter={[16, 16]} style={{ width: '100%' }}>
        <Col span={6}>
          <img
            src={product?.cover}
            alt={product?.name}
            style={{
              width: '100%',
              borderRadius: '10px',
              objectFit: 'cover',
            }}
          />
        </Col>

        <Col span={18}>
          <div style={{ marginBottom: '8px' }}>
            <Text strong style={{ fontSize: '16px' }}>
              {product.name}
            </Text>
          </div>
          <div style={{ marginBottom: '4px' }}>
            <Text strong>Số lượng: </Text>
            <Text>{product.qty} vé</Text>
          </div>
          <div style={{ marginBottom: '4px' }}>
            <Text strong>Thời gian mua: </Text>
            {/* <Text>{product?.createdDate}</Text> */}
          </div>
          <div style={{ marginBottom: '8px' }}>
            <Text strong>Giá: </Text>
            <Text>{product.buyNowPrice.toLocaleString()} VND</Text>
          </div>
          <div
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              textAlign: 'right',
            }}
          >
            <Text strong style={{ color: 'red' }}>
              Thành tiền: {product.buyNowPrice.toLocaleString()} VND
            </Text>
          </div>
        </Col>
      </Row>
    </Card>
  );
};

export default ProductItemVertical;
