import { Carousel, Col, Grid, Image, Row } from 'antd';
import { useRef, useState } from 'react';

const ImageProductCarousel = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const carouselRef = useRef(null);
  const { md } = Grid.useBreakpoint();
  const images = [
    'https://cdn.shoplucky.vn/files/8d5ed390926d4b2383a9a199537246fdWEBSPOTER111.png',
    'https://cdn.shoplucky.vn/files/63c1e4282c6b499a9d2e799aaf01d71cposter2.png',
    'https://cdn.shoplucky.vn/files/38724e8ddfad4377ad13d8eaa69ac70aPOSTER_WEB333.png',
    'https://cdn.shoplucky.vn/files/38724e8ddfad4377ad13d8eaa69ac70aPOSTER_WEB333.png',
    'https://cdn.shoplucky.vn/files/38724e8ddfad4377ad13d8eaa69ac70aPOSTER_WEB333.png',
    'https://cdn.shoplucky.vn/files/38724e8ddfad4377ad13d8eaa69ac70aPOSTER_WEB333.png',
    'https://cdn.shoplucky.vn/files/38724e8ddfad4377ad13d8eaa69ac70aPOSTER_WEB333.png',
    'https://cdn.shoplucky.vn/files/38724e8ddfad4377ad13d8eaa69ac70aPOSTER_WEB333.png',
  ];

  const handleThumbnailClick = (index) => {
    setActiveIndex(index);
    if (carouselRef.current) {
      carouselRef.current.goTo(index);
    }
  };

  return (
    // <div
    //   style={{
    //     padding: '20px',
    //     display: 'flex',
    //     alignItems: 'center',
    //     justifyContent: 'center',
    //   }}
    // >
    //   <div
    //     style={{
    //       display: 'flex',
    //       flexDirection: 'column',
    //       gap: '8px',
    //       marginRight: '16px',
    //     }}
    //   >
    //     {images.map((src, index) => (
    //       <div
    //         key={index}
    //         style={{
    //           border:
    //             activeIndex === index
    //               ? '2px solid #1890ff'
    //               : '2px solid transparent',
    //           borderRadius: '8px',
    //           cursor: 'pointer',
    //           overflow: 'hidden',
    //           width: '80px',
    //           height: '80px',
    //         }}
    //         onClick={() => handleThumbnailClick(index)}
    //       >
    //         <Image
    //           src={src}
    //           alt={`Thumbnail ${index + 1}`}
    //           preview={false}
    //           style={{
    //             width: '100%',
    //             height: '100%',
    //             objectFit: 'cover',
    //           }}
    //         />
    //       </div>
    //     ))}
    //   </div>

    //   <div style={{ width: '350px' }}>
    //     <Carousel
    //       ref={carouselRef}
    //       dots={false}
    //       effect="fade"
    //       afterChange={(current) => setActiveIndex(current)}
    //     >
    //       {images.map((src, index) => (
    //         <div key={index}>
    //           <Image
    //             src={src}
    //             alt={`Image ${index + 1}`}
    //             style={{
    //               width: '100%',
    //               maxHeight: '350px',
    //               objectFit: 'cover',
    //               borderRadius: '8px',
    //             }}
    //           />
    //         </div>
    //       ))}
    //     </Carousel>
    //   </div>
    // </div>
    <div
      style={{
        padding: '20px',
        display: 'flex',
        flexDirection: !md ? 'column' : 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <div
        style={{
          // display: 'flex',
          // flexDirection: !md ? 'row' : 'column',
          gap: '8px',
          // marginRight: !md ? '0px' : '16px',
          // marginTop: !md ? '16px' : '0px',
          overflowY: 'auto',
          maxHeight: 245,
        }}
      >
        {images.map((src, index) => (
          <div
            key={index}
            style={{
              border:
                activeIndex === index
                  ? '2px solid #1890ff'
                  : '2px solid transparent',
              borderRadius: '8px',
              cursor: 'pointer',
              overflow: 'hidden',
              width: !md ? '60px' : '80px',
              height: !md ? '60px' : '80px',
            }}
            onClick={() => handleThumbnailClick(index)}
          >
            <Image
              src={src}
              alt={`Thumbnail ${index + 1}`}
              preview={false}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
            />
          </div>
        ))}
      </div>

      <div style={{ width: !md ? '100%' : '350px' }}>
        <Carousel
          ref={carouselRef}
          dots={false}
          effect="fade"
          afterChange={(current) => setActiveIndex(current)}
        >
          {images.map((src, index) => (
            <div key={index}>
              <Image
                src={src}
                alt={`Image ${index + 1}`}
                style={{
                  width: '100%',
                  maxHeight: '350px',
                  objectFit: 'cover',
                  borderRadius: '8px',
                }}
              />
            </div>
          ))}
        </Carousel>
      </div>
    </div>
  );
};
export default ImageProductCarousel;
