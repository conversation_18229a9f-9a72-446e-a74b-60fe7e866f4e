import {
  bgColor,
  redColor,
  textColorBase,
  white,
} from '@/constants/themeColor';
import { ProductDetailDto } from '@/dto/product.dto';
import { ROUTES } from '@/enums/Routes';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import {
  convertCurrency,
  formatPriceVND,
  formatVND,
} from '@/utils/numberFormat';
import { ShoppingCartOutlined } from '@ant-design/icons';
import { Button, Flex, Grid, Progress, Tooltip } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, useNavigate } from 'react-router-dom';
import ProgressLine from '../common/ProgressLine';
import { useContentLang } from '@/hooks/useContentLang';

const productItemWrapper: CSSProperties = {
  borderRadius: 4,
  padding: 5,
  boxShadow: 'rgba(0, 0, 0, 0.1) 0px 10px 50px',
  position: 'relative',
  background: white,
  border: `1px solid ${bgColor}`,
  overflow: 'hidden',
  width: '100%',
};

const productImgStyles: CSSProperties = {
  width: '100%',
  minWidth: 86,
  minHeight: 86,
  borderRadius: 6,
  backgroundPosition: 'center',
  backgroundSize: '100% 100%',
};

const productNameStyles: CSSProperties = {
  color: '#212121',
  width: '100%',
  fontWeight: '400',
  display: '-webkit-box',
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  cursor: 'pointer',
};

const productPriceStyles: CSSProperties = {
  color: redColor,
  display: '-webkit-box',
  WebkitLineClamp: 1,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
};

const productQuantityStyles: CSSProperties = {
  color: '#9E9E9E',
  fontSize: 12,
  display: '-webkit-box',
  WebkitLineClamp: 1,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
};

const ProductItemOverlay: FC<{ item: ProductDetailDto }> = ({ item }) => {
  const { t } = useTranslation();
  const { xl, md, sm, xs } = Grid.useBreakpoint();
  const { handleUpdateCart, startTransition } = useGlobalContext();
  const navigate = useNavigate();

  const name = useContentLang(item);

  const onClickCard = () => {
    startTransition(() => {
      const url = generatePath(ROUTES.PRODUCT_DETAILS, {
        id: item.id.toString(),
      });

      navigate(url);
    });
  };

  return (
    <div
      style={productItemWrapper}
      className="cursor-pointer"
      onClick={onClickCard}
    >
      <Flex vertical align="center" justify="space-between">
        <img
          className="product-cover"
          src={item.cover}
          alt={name}
          style={{
            ...productImgStyles,
            // height: !xl ? (md ? 200 : sm ? 180 : 130) : 220,
            objectFit: 'contain',
          }}
        />
        <Tooltip title={name}>
          <span
            className="product-name"
            style={{
              ...productNameStyles,
              WebkitLineClamp: md ? 2 : 1,
              minHeight: md ? 40 : 20,
              fontSize: md ? 15 : 14,
              margin: md ? '10px 0' : '5px 0 0 0',
            }}
          >
            {name}
          </span>
        </Tooltip>

        {/* <Flex
          style={{ width: '100%' }}
          // gap={5}
          align="center"
          justify="space-between"
        >
          <span
            style={{
              ...productPriceStyles,
              fontSize: md ? 16 : 14,
              fontWeight: md ? 500 : 700,
            }}
          >
            {formatPriceVND(item.buyNowPrice)}
          </span>
          <span style={productQuantityStyles}>{t('home.sold')}</span>
        </Flex> */}
      </Flex>
    </div>
  );
};

export default ProductItemOverlay;
