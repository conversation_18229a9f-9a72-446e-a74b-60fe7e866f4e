import { Grid, Image } from 'antd';
import Slider from 'react-slick';
import { useRef } from 'react';
import './_image-slider.scss';

interface ImageProductsSliderProps {
  images: string[];
}

function ImageProductsSlider({ images }: ImageProductsSliderProps) {
  const { md } = Grid.useBreakpoint();
  const mainSliderRef = useRef<Slider | null>(null);
  const thumbSliderRef = useRef<Slider | null>(null);

  const mainSettings = {
    asNavFor: thumbSliderRef.current as Slider,
    ref: mainSliderRef,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: true,
    fade: true,
  };

  const thumbSettings = {
    asNavFor: mainSliderRef.current as Slider,
    ref: thumbSliderRef,
    slidesToShow: md ? 8 : 4,
    slidesToScroll: 1,
    swipeToSlide: true,
    focusOnSelect: true,
    arrows: true,
    infinity: false,
  };

  return (
    <div className="slider-container">
      {images.length > 1 ? (
        <>
          <Slider {...mainSettings}>
            {images.map((img, index) => (
              <div key={index}>
                <Image
                  src={img}
                  width={md ? 450 : '100%'}
                  height={md ? 450 : '100%'}
                  style={{
                    cursor: 'pointer',
                    background: '#F37421',
                    border: 'none',
                  }}
                />
              </div>
            ))}
          </Slider>

          <div className="thumbnail-slider" style={{ marginTop: 16 }}>
            <Slider {...thumbSettings}>
              {images.map((img, index) => (
                <div key={index}>
                  <img
                    src={img}
                    alt={`thumb-${index}`}
                    width={50}
                    height={50}
                    style={{
                      objectFit: 'cover',
                      borderRadius: 4,
                      margin: '0 auto',
                      cursor: 'pointer',
                    }}
                  />
                </div>
              ))}
            </Slider>
          </div>
        </>
      ) : (
        <div style={{ textAlign: 'center' }}>
          <Image
            src={images[0]}
            width={md ? 450 : '100%'}
            height={md ? 450 : '100%'}
            style={{
              cursor: 'pointer',
              background: '#F37421',
              border: 'none',
            }}
          />
        </div>
      )}
    </div>
  );
}

export default ImageProductsSlider;
