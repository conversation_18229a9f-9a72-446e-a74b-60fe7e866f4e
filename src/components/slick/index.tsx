import './style.scss';

import { Icons } from '@/components';
import { FC, ReactNode, useMemo } from 'react';
import Slider, { Settings } from 'react-slick';

export type ResponsiveType = {
  breakpoint: number;
  settings: Settings;
};

interface ArrowProps {
  children: ReactNode;
  slideCount?: number;
  currentSlide?: number;
}

interface SlickProps {
  children?: ReactNode;
  responsive?: ResponsiveType[];
}

const SlickButtonFix = ({
  children,
  slideCount,
  currentSlide,
  ...others
}: ArrowProps) => <span {...others}>{children}</span>;

const Slick: FC<SlickProps> = ({ children, responsive }) => {
  const settings: Settings = useMemo(
    () => ({
      responsive,
      slidesToShow: 10,
      speed: 1000,
      slidesToScroll: 3,
      // autoplaySpeed: 4000,
      // autoplay: true,
      infinite: true,
      arrows: true,
      nextArrow: (
        <SlickButtonFix>
          <Icons src="slide-left" size={48} />
        </SlickButtonFix>
      ),
      prevArrow: (
        <SlickButtonFix>
          <Icons src="slide-right" size={48} />
        </SlickButtonFix>
      ),
    }),
    [responsive]
  );

  return (
    <div className="slider-container">
      <Slider {...settings}>{children}</Slider>
    </div>
  );
};

export default Slick;
