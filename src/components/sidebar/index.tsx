import { FC, memo } from 'react';
import { Layout, Menu } from 'antd';
import { ItemType, MenuItemType } from 'antd/es/menu/interface';
import { MenuInfo } from 'rc-menu/lib/interface';

interface SidebarProps {
  hidden?: boolean;
  items?: ItemType<MenuItemType>[];
  defaultSelectedKeys?: string[];
  defaultOpenKeys?: string[];
  onClick?: (info: MenuInfo) => void;
}

const Sidebar: FC<SidebarProps> = ({ onClick, ...props }) => {
  return (
    <Layout.Sider
      width={200}
      style={{
        background: '#fff',
        display: props.hidden || !props?.items?.length ? 'none' : 'block',
      }}
    >
      <Menu
        {...props}
        mode="inline"
        style={{ height: '100%' }}
        onClick={onClick}
      />
    </Layout.Sider>
  );
};

export default memo(Sidebar);
