import { FC } from 'react';

export type Icons =
  | 'chat'
  | 'mall'
  | 'mall-orange'
  | 'clock-alarm'
  | 'packaging'
  | 'star'
  | 'time'
  | 'right-arrow'
  | 'slide-left'
  | 'slide-right'
  | 'cash'
  | 'facebook'
  | 'momo'
  | 'youtube'
  | 'zalo'
  | 'zalo-pay'
  | 'shipping'
  | 'gift'
  | 'cart-empty'
  | 'shipping-order'
  | 'location'
  | 'payment'
  | 'tiktok'
  | 'home'
  | 'voucher-icon'
  | 'product-icon'
  | 'phone-icon'
  | 'profile-icon'
  | 'wallet'
  | 'cart-red'
  | 'voucher-red'
  | 'notice'
  | 'vnpay'
  | 'home-orange-icon'
  | 'voucher-orange-icon'
  | 'product-orange-icon'
  | 'profile-orange-icon'
  | 'read-all-icon';

interface IconProps {
  src?: Icons;
  size?: number;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
  hidden?: boolean;
  onClick?: () => void;
}

const Index: FC<IconProps> = ({ src, size, width, height, ...props }) => {
  const icons: Record<Icons, string> = {
    chat: new URL('@public/images/chat.svg', import.meta.url).href,
    mall: new URL('@public/images/mall.svg', import.meta.url).href,
    packaging: new URL('@public/images/packaging.svg', import.meta.url).href,
    star: new URL('@public/images/star.svg', import.meta.url).href,
    time: new URL('@public/images/time.svg', import.meta.url).href,
    'mall-orange': new URL('@public/images/mall-orange.svg', import.meta.url)
      .href,
    'clock-alarm': new URL('@public/images/clock-alarm.svg', import.meta.url)
      .href,
    'right-arrow': new URL('@public/images/right.svg', import.meta.url).href,
    'slide-left': new URL('@public/images/slide-left.svg', import.meta.url)
      .href,
    'slide-right': new URL('@public/images/slide-right.svg', import.meta.url)
      .href,
    cash: new URL('@public/images/cash-icon.svg', import.meta.url).href,
    facebook: new URL('@public/images/facebook-icon.svg', import.meta.url).href,
    momo: new URL('@public/images/momo-icon.svg', import.meta.url).href,
    youtube: new URL('@public/images/youtube-icon.svg', import.meta.url).href,
    zalo: new URL('@public/images/zalo-icon.svg', import.meta.url).href,
    tiktok: new URL('@public/images/tiktok-svgrepo-com.svg', import.meta.url)
      .href,
    'zalo-pay': new URL('@public/images/zalopay-icon.svg', import.meta.url)
      .href,
    gift: new URL('@public/images/gift.svg', import.meta.url).href,
    shipping: new URL('@public/images/shipping.svg', import.meta.url).href,
    'cart-empty': new URL('@public/images/cart-empty.svg', import.meta.url)
      .href,
    'shipping-order': new URL(
      '@public/images/shipping-order.svg',
      import.meta.url
    ).href,
    location: new URL('@public/images/location.svg', import.meta.url).href,
    payment: new URL('@public/images/payment.svg', import.meta.url).href,
    home: new URL('@public/images/home-icon.svg', import.meta.url).href,
    'voucher-icon': new URL(
      '@public/images/voucher-icon-1.svg',
      import.meta.url
    ).href,
    'product-icon': new URL('@public/images/product-icon.svg', import.meta.url)
      .href,
    'phone-icon': new URL('@public/images/phone-icon.svg', import.meta.url)
      .href,
    'profile-icon': new URL('@public/images/profile-icon.svg', import.meta.url)
      .href,
    wallet: new URL('@public/images/wallet-icon.svg', import.meta.url).href,
    'cart-red': new URL('@public/images/cart-icon-red.svg', import.meta.url)
      .href,
    'voucher-red': new URL(
      '@public/images/voucher-red-icon.svg',
      import.meta.url
    ).href,
    notice: new URL('@public/images/notice-icon.svg', import.meta.url).href,
    vnpay: new URL('@public/images/vnpay.svg', import.meta.url).href,
    'home-orange-icon': new URL(
      '@public/images/home-orange-icon.svg',
      import.meta.url
    ).href,
    'voucher-orange-icon': new URL(
      '@public/images/voucher-orange-icon-1.svg',
      import.meta.url
    ).href,
    'product-orange-icon': new URL(
      '@public/images/product-orange-icon.svg',
      import.meta.url
    ).href,
    'profile-orange-icon': new URL(
      '@public/images/profile-orange-icon.svg',
      import.meta.url
    ).href,
    'read-all-icon': new URL(
      '@public/images/read-all-icon.svg',
      import.meta.url
    ).href,
  };

  return (
    <img
      {...props}
      loading="lazy"
      width={size || width}
      height={size || height}
      src={src ? icons[src] : undefined}
    />
  );
};

export default Index;
