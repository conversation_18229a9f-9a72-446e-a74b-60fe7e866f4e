import React from 'react';
import { render, screen } from '@testing-library/react';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';

// Mock component to test the hook
const TestComponent: React.FC<{
  hasMore: boolean;
  isLoading: boolean;
  onLoadMore: () => void;
}> = ({ hasMore, isLoading, onLoadMore }) => {
  const { loadingRef } = useInfiniteScroll({
    hasMore,
    isLoading,
    onLoadMore,
    threshold: 100,
  });

  return (
    <div>
      <div>Content</div>
      <div ref={loadingRef} data-testid="loading-trigger">
        {isLoading ? 'Loading...' : hasMore ? 'Scroll for more' : 'No more data'}
      </div>
    </div>
  );
};

export default TestComponent;
