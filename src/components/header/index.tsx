import TextWithIcon from '@/components/common/TextWithIcon';
import { white } from '@/constants/themeColor';
import { NO_AUTH_ROUTES, ROUTES } from '@/enums/Routes';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import {
  BellOutlined,
  MenuOutlined,
  ShoppingCartOutlined,
  HomeOutlined,
  UserOutlined,
  ShopOutlined,
  TagOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import {
  AutoComplete,
  AutoCompleteProps,
  Avatar,
  Badge,
  Button,
  Drawer,
  Dropdown,
  Flex,
  Grid,
  Menu,
  Typography,
} from 'antd';
import { CSSProperties, FC, JSX, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, useLocation, useNavigate } from 'react-router-dom';
import Language from '../language';
import { useAuthStore } from '@/stores/authStore';
import Container from '@/components/home/<USER>';
import NotificationDropdown from '../notification-dropdown';
import { useNotifications } from '@/views/notification/hooks/useNotifications';
import SearchDropdown from '../search-dropdown/SearchDropdown';
import logo from '/images/logo_web.png';
import cartImg from '/images/cart_artboard.png';
import PromoSection from '../home/<USER>';
import { Icons } from '..';
import '../../assets/styles/_header.scss';

interface headerProps {}

export interface MenuItem {
  text?: string;
  element: JSX.Element;
  onClick?: () => void;
  hidden?: boolean;
}

const iconMenuFooterStyle: CSSProperties = {
  fontSize: 20,
};

const menuStyle: CSSProperties = {
  display: 'flex',
  justifyContent: 'space-around',
  position: 'fixed',
  bottom: 0,
  left: 0,
  // paddingTop: 10,
  zIndex: 9999,
};

const menuItemStyle: CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  minWidth: 70,
  padding: '10px 0 0 0',
  height: 60,
  lineHeight: '25px',
};

const mobileIconBase: CSSProperties = {
  width: 30,
  height: 30,
};

const iconStyle: CSSProperties = {
  fontSize: 20,
  color: '#fff',
};

const headerStyle: CSSProperties = {
  position: 'fixed',
  top: 0,
  zIndex: 2,
  width: '100%',
  maxHeight: 90,
};

const labelStyle: CSSProperties = {
  fontSize: 15,
  fontWeight: 400,
};

const labelLeftStyle: CSSProperties = {
  fontSize: 12,
  fontWeight: 400,
  color: '#ffffffe6',
};

const HeaderContainer: FC<headerProps> = () => {
  const [visible, setVisible] = useState(false);
  const { t } = useTranslation();
  const { xl } = Grid.useBreakpoint();
  const { cart, startTransition } = useGlobalContext();
  const navigate = useNavigate();
  const { logout, member } = useAuthStore();
  const { data: listNotifications, total: noticeTotal } = useNotifications();
  const { md } = Grid.useBreakpoint();
  const location = useLocation();
  const activeMenu = location.pathname.split('/')[1];
  const activeCartMenu = location.pathname;

  const onLogout = () => {
    startTransition(() => {
      logout();
      navigate('/');
    });
  };

  const gotoLogin = () => {
    startTransition(() => {
      navigate('/login');
    });
  };

  const gotoProfile = () => {
    startTransition(() => {
      navigate('/profile/1');
    });
  };

  const onNavigate = (url: string) => {
    startTransition(() => {
      navigate(url);
    });
  };

  const onNavigateToProducts = () => {
    startTransition(() => {
      const url = generatePath(ROUTES.PRODUCT_LIST, {
        categoryId: 'all',
      });

      navigate(url);
    });
  };
  const onNavigateToVoucher = () => {
    startTransition(() => {
      const url = generatePath(ROUTES.VOUCHER, {
        categoryId: 'all',
      });

      navigate(url);
    });
  };
  const onNavigateToAboutUs = () => {
    startTransition(() => {
      navigate(ROUTES.ABOUTUS);
    });
  };

  const showDrawer = () => {
    setVisible(true);
  };

  const closeDrawer = () => {
    setVisible(false);
  };

  const mockVal = (str: string, repeat = 1) => ({
    value: str.repeat(repeat),
  });

  const handleCallContact = () => {
    window.location.href = 'tel:0909689026';
  };

  const items: MenuProps['items'] = [
    {
      key: 'profile',
      label: (
        <Typography.Text onClick={gotoProfile}>
          {t('auth.profile')}
        </Typography.Text>
      ),
    },
    // {
    //   key: 'orders',
    //   label: <Typography.Text>Orders</Typography.Text>,
    // },
    {
      key: 'logout',
      label: (
        <Typography.Text onClick={onLogout}>{t('auth.logout')}</Typography.Text>
      ),
    },
  ];

  const navs = [
    {
      key: ROUTES.HOME,
      label: (
        <Typography.Text
          style={labelLeftStyle}
          onClick={onNavigate.bind(null, ROUTES.HOME)}
        >
          {t('header.home')}
        </Typography.Text>
      ),
    },
    {
      key: ROUTES.PRODUCT_LIST,
      label: (
        <Typography.Text style={labelLeftStyle} onClick={onNavigateToProducts}>
          {t('header.products')}
        </Typography.Text>
      ),
    },
    {
      key: '/aboutUs',
      label: (
        <Typography.Text style={labelLeftStyle} onClick={onNavigateToAboutUs}>
          {t('header.aboutUs')}
        </Typography.Text>
      ),
    },
    // {
    //   key: ROUTES.VOUCHER,
    //   label: (
    //     <Typography.Text style={labelLeftStyle} onClick={onNavigateToVoucher}>
    //       {t('header.voucher')}
    //     </Typography.Text>
    //   ),
    // },
  ];

  const menuMobile = [
    {
      key: ROUTES.HOME,
      label: (
        <Typography.Text
          style={labelLeftStyle}
          onClick={onNavigate.bind(null, ROUTES.HOME)}
        >
          {t('header.home')}
        </Typography.Text>
      ),
    },
    {
      key: ROUTES.PRODUCT_LIST,
      label: (
        <Typography.Text style={labelLeftStyle} onClick={onNavigateToProducts}>
          {t('header.products')}
        </Typography.Text>
      ),
    },
    {
      key: ROUTES.ABOUTUS,
      label: (
        <Typography.Text style={labelLeftStyle} onClick={onNavigateToAboutUs}>
          {t('header.aboutUs')}
        </Typography.Text>
      ),
    },
    {
      key: ROUTES.VOUCHER,
      label: (
        <Typography.Text style={labelLeftStyle} onClick={onNavigateToVoucher}>
          {t('header.voucher')}
        </Typography.Text>
      ),
    },
    {
      key: 'users',
      icon: <UserOutlined />,
      label: (
        <Typography.Text style={labelLeftStyle}>
          <span style={{ fontSize: 15 }}>{member?.username}</span>
        </Typography.Text>
      ),
      children: [
        {
          key: '8',
          label: (
            <Typography.Text onClick={gotoProfile}>
              {t('auth.profile')}
            </Typography.Text>
          ),
        },
        !member && {
          key: '7',
          label: (
            <Typography.Text onClick={gotoLogin}>
              {t('auth.login')}
            </Typography.Text>
          ),
        },
        member && {
          key: 'logout',
          label: (
            <Typography.Text onClick={onLogout}>
              {t('auth.logout')}
            </Typography.Text>
          ),
        },
      ],
    },
    {
      key: 'language',
      label: <Language />,
    },
  ];

  const itemsFooter = [
    {
      key: 'home',
      label: (
        <span
          style={{
            color: activeMenu === '' ? '#F37421' : '#000000E0',
          }}
        >
          {t('header.home')}
        </span>
      ),
      icon:
        activeMenu === '' ? (
          <Icons src="home-orange-icon" size={20} />
        ) : (
          <Icons src="home" size={20} />
        ),
      onClick: onNavigate.bind(null, ROUTES.HOME),
    },
    {
      key: 'voucher',
      label: (
        <span
          style={{
            color: activeMenu === 'voucher' ? '#F37421' : '#000000E0',
          }}
        >
          {t('header.voucher')}
        </span>
      ),
      icon:
        activeMenu === 'voucher' ? (
          <Icons src="voucher-orange-icon" size={20} />
        ) : (
          <Icons src="voucher-icon" size={20} />
        ),
      onClick: onNavigateToVoucher,
    },
    {
      key: 'cart',
      label: (
        <span
          style={{
            color:
              activeCartMenu === '/products/cart' ? '#F37421' : '#000000E0',
          }}
        >
          {t('header.cart')}
        </span>
      ),
      icon:
        activeCartMenu === '/products/cart' ? (
          <Badge
            count={cart.length}
            style={{ top: 3, left: 10, lineHeight: 1.5 }}
          >
            <Icons src="product-orange-icon" size={20} />
          </Badge>
        ) : (
          <Badge
            count={cart.length}
            style={{ top: 3, left: 10, lineHeight: 1.5 }}
          >
            <Icons src="product-icon" size={20} />
          </Badge>
        ),
      onClick: onNavigate.bind(null, ROUTES.PRODUCT_CART),
    },
    // {
    //   key: 'products',
    //   label: (
    //     <span
    //       style={{
    //         color: activeMenu === 'products' ? '#F37421' : '#000000E0',
    //       }}
    //     >
    //       {t('header.products')}
    //     </span>
    //   ),
    //   icon:
    //     activeMenu === 'products' ? (
    //       <Icons src="product-orange-icon" size={20} />
    //     ) : (
    //       <Icons src="product-icon" size={20} />
    //     ),
    //   onClick: onNavigateToProducts,
    // },
    // {
    //   key: 'orders',
    //   label: t('header.cart'),
    //   icon: (
    //     <Badge
    //       count={cart.length}
    //       offset={[0, -5]}
    //       style={{ top: 15, margin: 0, paddingTop: 7 }}
    //     >
    //       <ShoppingCartOutlined style={{ ...iconMenuFooterStyle }} />
    //     </Badge>
    //   ),
    //   onClick: onNavigate.bind(null, ROUTES.PRODUCT_CART),
    // },
    {
      key: 'contact',
      label: t('auth.cskh'),
      icon: <Icons src="phone-icon" size={20} />,
      onClick: handleCallContact,
    },
    {
      key: 'profile',
      label: (
        <span
          style={{
            color: activeMenu === 'profile' ? '#F37421' : '#000000E0',
          }}
        >
          {t('auth.profile')}
        </span>
      ),
      icon:
        activeMenu === 'profile' ? (
          <Icons src="profile-orange-icon" size={20} />
        ) : (
          <Icons src="profile-icon" size={20} />
        ),
      onClick: member ? gotoProfile : gotoLogin,
    },
  ];

  const rightItems: MenuItem[] = [
    {
      onClick: onNavigate.bind(null, ROUTES.PRODUCT_CART),
      element: (
        <Badge count={cart.length} offset={[0, -5]}>
          <ShoppingCartOutlined style={iconStyle} />
        </Badge>
      ),
    },
    {
      element: (
        <NotificationDropdown
          notifications={listNotifications}
        ></NotificationDropdown>
      ),
    },
  ];

  return (
    <>
      {NO_AUTH_ROUTES.includes(location.pathname) ? null : (
        <Container
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 1000,
            background: md
              ? 'linear-gradient(to bottom, #F6412E, #F37421)'
              : '#F37421',
            boxShadow: md ? '0 2px 10px 0 rgba(0, 0, 0, .1)' : 'none',
            padding: 15,
          }}
        >
          <Flex justify="space-between" gap={5} vertical>
            {md && (
              <div
                style={{
                  marginTop: 5,
                  color: '#fff',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <div style={{ display: 'flex', gap: 10 }}>
                  <span>{t('header.download_app')}</span>
                  <span>|</span>
                  <span>{t('header.support')}</span>
                </div>
                <div style={{ display: 'flex', gap: 20 }}>
                  <div
                    onClick={onNavigateToVoucher}
                    style={{ cursor: 'pointer' }}
                  >
                    {t('header.voucher')}
                  </div>
                  <Language />
                </div>
              </div>
            )}
            <Flex
              flex={1}
              gap={10}
              justify="flex-end"
              align={!xl ? 'flex-start' : 'center'}
              style={{
                // padding: '0 20px',
                height: '80px',
                alignItems: 'center',
              }}
            >
              {/* {!xl && (
                <Button
                  type="text"
                  icon={<MenuOutlined />}
                  onClick={showDrawer}
                  style={{ fontSize: '18px' }}
                />
              )} */}
              {/* {!md && (
                <Button
                  type="text"
                  icon={<MenuOutlined />}
                  onClick={showDrawer}
                  style={{ fontSize: '18px' }}
                />
              )} */}
              {md && (
                <div style={{ marginRight: 'auto' }}>
                  <img
                    loading="lazy"
                    src="/images/logo_shop_lucky.png"
                    alt="Logo"
                    onClick={onNavigate.bind(null, ROUTES.HOME)}
                    style={{ height: '60px', cursor: 'pointer' }}
                  />
                </div>
              )}
              {/* {xl && (
                <Flex justify="center">
                  <Menu
                    theme="light"
                    mode="horizontal"
                    defaultSelectedKeys={[location.pathname || ROUTES.HOME]}
                    items={nav}
                    style={{
                      display: 'flex',
                      flex: 1,
                      minWidth: 300,
                      color: '#000000',
                      fontSize: 18,
                      borderBottom: 'none',
                    }}
                  />
                </Flex>
              )} */}
              <div style={{ width: !md ? '100%' : 'auto' }}>
                <SearchDropdown />
                {/* {!md && (
                  <div
                    style={{ display: 'flex', gap: '13px', margin: '3px 0' }}
                  >
                    {navs.map((nav, idx) => {
                      return <a key={idx}>{nav.label}</a>;
                    })}
                  </div>
                )} */}
              </div>
              {!md && (
                <>
                  <NotificationDropdown
                    notifications={listNotifications}
                  ></NotificationDropdown>
                  <Language />
                </>
              )}

              {xl ? (
                <Flex gap={20}>
                  {rightItems?.map((item, index) => (
                    <TextWithIcon
                      styles={labelStyle}
                      key={`item-${index}`}
                      {...item}
                    />
                  ))}
                  {member && (
                    <Dropdown menu={{ items }} placement="bottomRight" arrow>
                      <Flex gap={5} onClick={gotoProfile} align="center">
                        {/* <UserOutlined style={iconStyle} /> */}
                        <Avatar size="large" src={member.avatar} />
                        <span style={{ fontSize: 15, color: '#fff' }}>
                          {member?.username}
                        </span>
                      </Flex>
                    </Dropdown>
                  )}
                  {!member && (
                    <Flex gap={5} align="center" onClick={gotoLogin}>
                      <UserOutlined style={iconStyle} />
                      <span style={{ fontSize: 15, color: '#fff' }}>
                        {t('auth.login')}
                      </span>
                    </Flex>
                  )}
                  {/* <Language /> */}
                </Flex>
              ) : (
                <>
                  {/* <Flex
                    style={{
                      ...mobileIconBase,
                      fontSize: 20,
                    }}
                    justify="center"
                  >
                    <Badge
                      count={cart.length}
                      offset={[0, -5]}
                      onClick={onNavigate.bind(null, ROUTES.PRODUCT_CART)}
                    >
                      <ShoppingCartOutlined style={iconStyle} />
                    </Badge>
                  </Flex> */}
                </>
              )}
            </Flex>
          </Flex>
          <Drawer placement="left" onClose={closeDrawer} open={visible}>
            <Menu
              theme="light"
              mode="inline"
              defaultSelectedKeys={[location.pathname || ROUTES.HOME]}
              items={menuMobile}
              style={{
                flex: 1,
                color: '#000000',
                fontSize: 18,
              }}
            />
          </Drawer>
          {!md && (
            <Menu mode="inline" selectable={false} style={{ ...menuStyle }}>
              {itemsFooter.map(({ key, label, icon, onClick }) => (
                <Menu.Item
                  key={key}
                  icon={icon}
                  style={{ ...menuItemStyle, margin: 0, fontSize: 13 }}
                  onClick={onClick}
                >
                  {label}
                </Menu.Item>
              ))}
            </Menu>
          )}
        </Container>
      )}
      {NO_AUTH_ROUTES.includes(location.pathname)
        ? null
        : !md && (
            <div
              style={{
                height: '60vh',
                backgroundColor: '#F37421',
                position: 'absolute',
                zIndex: 0,
                width: '100%',
                borderRadius: '0 0 20px 20px',
              }}
            ></div>
          )}
      {NO_AUTH_ROUTES.includes(location.pathname)
        ? null
        : !md && (
            <div
              style={{
                height: '50vh',
                backgroundColor: '#F37421',
                position: 'absolute',
                zIndex: 0,
                width: '100%',
                borderRadius: '0 0 20px 20px',
              }}
            ></div>
          )}
    </>
  );
};

export default HeaderContainer;
