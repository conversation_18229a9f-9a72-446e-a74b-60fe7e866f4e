import { ROUTES } from '@/enums/Routes';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { formatVND } from '@/utils/numberFormat';
import { Button, Divider, Flex, Typography } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import Container from '../home/<USER>';

const FooterStyles: CSSProperties = {
  gap: 20,
  background: '#fff',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'end',
};

const OrderFooter: FC = () => {
  const { t } = useTranslation();
  const {
    cart,
    normal,
    ticket,
    notify,
    handleRemove,
    setNormal,
    setTicket,
    startTransition,
  } = useGlobalContext();
  const navigate = useNavigate();

  const totalAmount = cart
    .filter(({ id, ticketId }) =>
      [...normal, ...ticket].includes([id, ticketId].join('-'))
    )
    .reduce<number>(
      (prev, { price, price_ticket, quantity }) =>
        prev + (price_ticket || price || 0) * quantity,
      0
    );

  const discount = -12000;

  const shipping = 22000;

  const onPurchase = () => {
    // TODO
    handleRemove([...normal, ...ticket]);
    setNormal([]);
    setTicket([]);

    notify.success('Purchase successfully');

    setTimeout(() => {
      startTransition(() => {
        navigate(ROUTES.HOME);
      });
    }, 2000);
  };

  return (
    <footer style={FooterStyles}>
      <Container>
        <Flex
          align="center"
          justify="space-between"
          style={{ minWidth: '23%' }}
        >
          <Typography.Text className="gray-darker">
            {t('order.total_amount')}
          </Typography.Text>
          <Typography.Text className="red-primary">
            {formatVND(totalAmount.toFixed(2))}
          </Typography.Text>
        </Flex>

        <Flex
          align="center"
          justify="space-between"
          style={{ minWidth: '23%' }}
        >
          <Typography.Text className="gray-darker">
            {t('order.delivery_fee')}
          </Typography.Text>
          <Typography.Text className="red-primary">
            {formatVND(shipping.toFixed(2))}
          </Typography.Text>
        </Flex>

        <Flex
          align="center"
          justify="space-between"
          style={{ minWidth: '23%' }}
        >
          <Typography.Text className="gray-darker">
            {t('order.discount')}
          </Typography.Text>
          <Typography.Text className="green-dark">
            {formatVND(discount.toFixed(2))}
          </Typography.Text>
        </Flex>

        <Divider style={{ margin: 0 }} />

        <Flex
          align="center"
          justify="space-between"
          style={{ minWidth: '23%' }}
        >
          <Typography.Text className="gray-darker">
            {t('order.total')}
          </Typography.Text>
          <Typography.Title
            className="red-primary"
            level={2}
            style={{ margin: 0 }}
          >
            {formatVND((totalAmount + shipping + discount).toFixed(2))}
          </Typography.Title>
        </Flex>

        <Button
          danger
          disabled={!totalAmount}
          type="primary"
          onClick={onPurchase}
          style={{
            width: 240,
            height: 50,
          }}
        >
          {t('button.purchase')}
        </Button>
      </Container>
    </footer>
  );
};

export default OrderFooter;
