import { Icons } from '@/components';
import { textColorBase } from '@/constants/themeColor';
import { Flex, Typography } from 'antd';
import { FC } from 'react';

import type { Icons as IconTypes } from '@/components/icons';

interface ContactItemProps {
  id?: string;
  img?: IconTypes;
  text?: string;
  link?: string;
}

export interface ContactProps {
  vertical?: boolean;
  title?: string;
  items?: ContactItemProps[];
  onItemClicked?: (item: ContactItemProps) => void;
}

const Contact: FC<ContactProps> = ({
  title,
  vertical,
  items,
  onItemClicked = () => {},
}) => {
  return (
    <Flex vertical align="left" gap={10}>
      <Typography.Title
        level={5}
        hidden={!title}
        style={{ color: textColorBase }}
      >
        {title}
      </Typography.Title>
      <Flex
        justify="flex-start"
        vertical={vertical}
        align={vertical ? 'left' : 'center'}
        hidden={!items || !items.length}
        gap={vertical ? 5 : 30}
      >
        {items?.map(({ id, img, text, link }, index) => (
          <Flex
            vertical
            key={`contact-${index}`}
            align={vertical ? 'left' : 'center'}
            justify={vertical ? 'flex-start' : 'space-between'}
            style={{
              minHeight: vertical ? 'fix-content' : 65,
              cursor: 'pointer',
            }}
            onClick={onItemClicked.bind(null, { img, text, id, link })}
          >
            <Icons src={img} hidden={!img} />
            <Typography.Text hidden={!text} style={{ color: textColorBase }}>
              {text}
            </Typography.Text>
          </Flex>
        ))}
      </Flex>
    </Flex>
  );
};

export default Contact;
