import { textColorBase } from '@/constants/themeColor';
import { ROUTES } from '@/enums/Routes';
import { Col, Grid, Layout, List, Row } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import Contact, { ContactProps } from './Contact';
import bct from '/images/logoSaleNoti.png';

const footerInfo: CSSProperties = {
  background: '#FAFCFF',
};

const Footer: FC = () => {
  const { pathname } = useLocation();
  const { t } = useTranslation();

  const listInfo: string[] = [
    t('footer.support.happyShop'),
    t('footer.info.officeAddress'),
    t('footer.info.hotline'),
    t('footer.info.copyright', { year: new Date().getFullYear() }),
    t('footer.info.issuer'),
    t('footer.info.businessLicense'),
  ];

  const contactInfo: ContactProps[] = [
    {
      vertical: true,
      title: t('footer.support.title'),
      items: [
        { text: t('footer.support.hotline') },
        // { text: t('footer.support.faq') },
        // { text: t('footer.support.supportRequest') },
        { text: t('footer.support.orderGuide'), link: '/order-guide' },
        { text: t('footer.support.shippingMethod'), link: '/shipping-method' },
        { text: t('footer.support.returnPolicy'), link: '/return-policy' },
        {
          text: t('footer.support.tradingConditions'),
          link: '/trading-conditions',
        },
        // { text: t('footer.support.installmentGuide') },
        // { text: t('footer.support.importPolicy') },
        // { text: t('footer.support.emailSupport') },
      ],
    },
    {
      vertical: true,
      title: t('footer.about.title'),
      items: [
        { text: t('footer.about.aboutUs'), link: '/about-us' },
        // { text: t('footer.about.careers') },
        { text: t('footer.about.paymentPolicy'), link: '/payment-policy' },
        { text: t('footer.about.privacyPolicy'), link: '/privacy-policy' },
        { text: t('footer.about.complaintPolicy'), link: '/complaints-policy' },
        {
          text: t('footer.about.inspectionPolicy'),
          link: '/inspection-policy',
        },
        {
          text: t('footer.about.warrantyPolicy'),
          link: '/warranty-policy',
        },
        // { text: t('footer.about.terms') },
        // { text: t('footer.about.b2bSales') },
        // { text: t('footer.about.salesChannel') },
      ],
    },
    {
      title: t('footer.payment.title'),
      items: [
        { img: 'cash', text: t('footer.payment.methods.cash') },
        { img: 'vnpay', text: t('footer.payment.methods.vnpay') },
      ],
    },
    {
      title: t('footer.social.title'),
      items: [
        {
          img: 'facebook',
          text: t('footer.social.facebook'),
          link: 'https://www.facebook.com/profile.php?id=61571284172832',
        },

        {
          img: 'tiktok',
          text: t('footer.social.tiktok'),
          link: 'https://www.tiktok.com/@shoplucky88',
        },
        {
          img: 'zalo',
          text: t('footer.social.zalo'),
          link: 'https://zalo.me/0909689026',
        },
      ],
    },
  ];

  const { md } = Grid.useBreakpoint();

  let renderFooter = (
    <Layout.Footer
      style={{ background: '#fff', overflow: 'hidden', padding: 0, zIndex: 1 }}
    >
      <Row
        justify="space-between"
        style={{ padding: md ? '20px 8vw' : 12, margin: 0, background: '#fff' }}
      >
        {contactInfo.map((info: ContactProps, idx) => (
          <Col
            xs={{ flex: '100%' }}
            sm={{ flex: '50%' }}
            md={{ flex: '50%' }}
            lg={{ flex: '24%' }}
            key={idx}
          >
            <Contact
              key={`contact-card-${idx}`}
              {...info}
              onItemClicked={(e) => {
                if (!e.link) return;
                window.open(`${e.link}`, '_blank');
              }}
            />
          </Col>
        ))}
      </Row>

      <div
        style={{
          ...footerInfo,
          padding: md ? '20px 8vw' : '12px 12px 70px 12px',
        }}
      >
        <List
          grid={{ column: 1 }}
          dataSource={listInfo}
          renderItem={(info: string, index) => (
            <List.Item key={index} style={{ color: textColorBase }}>
              {info}
            </List.Item>
          )}
        />
        <a href="http://online.gov.vn/Website/chi-tiet-130076" target="_blank">
          <img width={200} alt="" title="" src={bct} />
        </a>
      </div>
    </Layout.Footer>
  );

  switch (pathname) {
    // case ROUTES.PRODUCT_ORDER:
    //   renderFooter = <OrderFooter />;
    //   break;

    case ROUTES.LOGIN:
    case ROUTES.REGISTER:
    case ROUTES.FORGOT_PASSWORD:
    case ROUTES.OTP:
    case ROUTES.UPDATE_PASSWORD:
      renderFooter = null;
      break;
  }

  return renderFooter;
};

export default Footer;
