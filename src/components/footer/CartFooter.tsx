import { ROUTES } from '@/enums/Routes';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { formatVND } from '@/utils/numberFormat';
import { Button, Typography } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const FooterStyles: CSSProperties = {
  height: 112,
  gap: 50,
  background: '#fff',
  display: 'flex',
  justifyContent: 'flex-end',
  alignItems: 'baseline',
};

const CartFooter: FC = () => {
  const { t } = useTranslation();
  const { cart, normal, ticket, startTransition } = useGlobalContext();
  const navigate = useNavigate();

  const totalAmount = cart
    .filter(({ id, ticketId }) =>
      [...normal, ...ticket].includes([id, ticketId].join('-'))
    )
    .reduce<number>(
      (prev, { price, price_ticket, quantity }) =>
        prev + (price_ticket || price || 0) * quantity,
      0
    );

  const onPurchase = () => {
    startTransition(() => {
      navigate(ROUTES.PRODUCT_ORDER);
    });
  };

  return (
    <footer style={FooterStyles}>
      <Typography.Title level={4} className="gray-darker">
        {t('footer.total')}
      </Typography.Title>
      <Typography.Title level={4} className="red-primary">
        {formatVND(totalAmount.toFixed(2))}
      </Typography.Title>
      <Button
        danger
        type="primary"
        disabled={!normal.length && !ticket.length}
        onClick={onPurchase}
        style={{
          width: 240,
          height: 50,
        }}
      >
        {t('button.purchase')}
      </Button>
    </footer>
  );
};

export default CartFooter;
