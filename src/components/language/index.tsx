import { textColorBase } from '@/constants/themeColor';
import { LANGUAGE } from '@/enums/Language';
import { Dropdown, Flex, MenuProps } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';

const baseSize: CSSProperties = {
  width: 25,
};

const textStyle: CSSProperties = {
  fontSize: 15,
  cursor: 'pointer',
  color: '#ffffff'
};

const countryFlag = (imgSrc: string, text: string) => {
  return (
    <Flex gap={5} align="center">
      <img style={baseSize} src={imgSrc} loading="lazy" />
      <span style={{ ...textStyle, color: textColorBase }}>{text}</span>
    </Flex>
  );
};

const Language: FC = ({}) => {
  const { i18n, t } = useTranslation();
  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    localStorage.setItem('lang', key);
    i18n.changeLanguage(key);
  };

  const items: MenuProps['items'] = [
    {
      key: LANGUAGE.VI,
      label: countryFlag('/images/vietnam.png', t('languages.vietnam')),
    },
    {
      key: LANGUAGE.EN,
      label: countryFlag('/images/us.png', t('languages.english')),
    },
    {
      key: LANGUAGE.CN,
      label: countryFlag('/images/china.png', t('languages.chinese')),
    },
  ];

  const imgs: {
    [key: string]: string;
  } = {
    vi: '/images/vietnam.png',
    cn: '/images/china.png',
    en: '/images/us.png',
  };

  const menuProps = {
    items: items?.filter((item) => item?.key !== i18n.language),
    onClick: handleMenuClick,
  };

  return (
    <Dropdown placement="bottomRight" menu={menuProps} trigger={['click']}>
      <Flex align="center" gap={6}>
        <img style={baseSize} src={imgs?.[i18n.language]} loading="lazy" />
        <span style={textStyle}>{t('language')}</span>
      </Flex>
    </Dropdown>
  );
};
export default Language;
