import { Flex } from 'antd';
import { CSSProperties, FC, JSX } from 'react';
import Text from './Text';

interface TextWithIconProps {
  text?: string;
  element: JSX.Element;
  styles?: CSSProperties;
  onClick?: () => void;
}

const baseStyles: CSSProperties = {
  cursor: 'pointer',
};

const TextWithIcon: FC<TextWithIconProps> = ({
  text,
  element,
  styles,
  onClick,
}) => (
  <Flex
    gap={5}
    align="center"
    style={{ ...baseStyles, ...styles }}
    onClick={onClick}
  >
    {element}
    <Text>{text}</Text>
  </Flex>
);

export default TextWithIcon;
