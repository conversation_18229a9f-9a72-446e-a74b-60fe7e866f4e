import { FC } from 'react';
import { Checkbox, CheckboxChangeEvent } from 'antd';
import './CustomCheckbox.css';

interface CustomCheckboxProps {
  checked?: boolean;
  onChange?: (e: CheckboxChangeEvent) => void;
  children?: React.ReactNode;
}

const CustomCheckbox: FC<CustomCheckboxProps> = ({
  checked,
  onChange,
  children,
}) => {
  return (
    <Checkbox
      checked={checked}
      onChange={onChange}
      children={children}
    />
  );
};

export default CustomCheckbox;
