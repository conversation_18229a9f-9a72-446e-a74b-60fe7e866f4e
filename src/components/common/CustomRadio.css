.custom-radio {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row-reverse; /* Text before Radio */
  gap: 8px;
  padding: 8px 12px;
}

/* Align text to the start */
.custom-radio .ant-radio {
  text-align: end;
}

/* Align text to the start */
.custom-radio span:not(.ant-radio) {
  flex-grow: 1;
  text-align: start;
}

.custom-radio .ant-radio-inner,
.ant-radio-inner:hover {
  background-color: #eae8e8;
  border: 0;
}

.custom-radio .ant-radio-checked .ant-radio-inner {
  border-color: #eae8e8 !important;
  background-color: #eae8e8 !important;
}

.custom-radio .ant-radio-inner::after {
  background-color: #ff0909 !important;
  inset-block-start: 40%;
  inset-inline-start: 40%;
  width: 20px;
  height: 20px;
}
