import { Progress } from 'antd';
import { CSSProperties, FC } from 'react';
import { yellowGradient } from '@/constants/themeColor';
import './CustomProgress.css';

interface IProgressLineProps {
  current: number;
  total: number;
  styles?: CSSProperties;
  className?: string;
}

const ProgressLine: FC<IProgressLineProps> = ({
  current,
  total,
  styles,
  className,
}) => (
  <Progress
    status="active"
    strokeColor={yellowGradient}
    percent={(current / total) * 100}
    showInfo={false}
    style={styles}
    className={className}
  />
);

export default ProgressLine;
