import React, { CSSProperties, useEffect, useState } from 'react';
import { Dropdown, List, Button, Badge, Tag, Flex } from 'antd';
import { BellFilled, BellOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { redColor } from '@/constants/themeColor';
import { ListNotificationsDto } from '@/dto/auth-member.dto';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { Icons } from '..';
import { useNotifications } from '@/views/notification/hooks/useNotifications';

const notificationListsStyle: CSSProperties = {
  width: 360,
  backgroundColor: '#fff',
  border: '1px solid #f0f0f0',
  borderRadius: 8,
};

interface NotificationDropdownProps {
  notifications: ListNotificationsDto[];
}

const NotificationDropdown = ({ notifications }: NotificationDropdownProps) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { startTransition } = useGlobalContext();
  const [listNotifications, setListNotifications] = useState(notifications);
  const [isOpen, setIsOpen] = useState(false);
  const [idReadList, setIdReadList] = useState<string[]>([]);
  const [numberNoticeState, setNumberNoticeState] = useState(0);
  const { handleMarkAsRead: handleMarkAsReadApi } = useNotifications();

  const bellYellowImg = '/images/bell.png';

  const gotoProfile = () => {
    startTransition(() => {
      navigate('/profile/2');
    });
  };

  useEffect(() => {
    setListNotifications(notifications);
  }, [notifications]);

  useEffect(() => {
    setNumberNoticeState(listNotifications?.filter((n) => !n.isRead).length);
  }, [listNotifications]);

  const handleMarkAsRead = (id) => {
    setListNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, isRead: true } : n))
    );
    setIdReadList((prev) => [...prev, id]);
    setNumberNoticeState((prev) => prev - 1);
  };

  const handleOpenChange = (flag) => {
    setIsOpen(flag);
    if (!flag) {
      onCloseNotice();
    }
  };

  const onCloseNotice = () => {
    handleMarkAsReadApi(idReadList);
  };

  const menu = (
    <div
      style={{
        ...notificationListsStyle,
      }}
    >
      <List
        style={{
          maxHeight: 600,
          overflowY: 'auto',
        }}
        size="small"
        dataSource={listNotifications}
        renderItem={(item) => (
          <List.Item
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
            onClick={handleMarkAsRead.bind(null, item.id)}
            key={item.id}
            extra={
              !item.isRead && (
                <Tag
                  color="red"
                  style={{ padding: 0, width: 10, height: 10 }}
                ></Tag>
              )
            }
          >
            <List.Item.Meta
              style={{ alignItems: 'center' }}
              avatar={
                <img
                  src={bellYellowImg}
                  width={40}
                  height={40}
                  alt="Bell Icon"
                />
              }
              title={
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <span
                    style={{
                      fontWeight: item.isRead ? 'normal' : 'bold',
                      fontSize: 16,
                      color: '#f35245',
                    }}
                  >
                    {i18n.language === 'vi'
                      ? item.titleVi
                      : i18n.language === 'en'
                        ? item.title
                        : item.titleCn}
                  </span>
                  <span
                    style={{
                      color: '#aaa',
                      fontSize: 12,
                      fontWeight: 'normal',
                    }}
                  >
                    {dayjs(item.createdDate).format('DD/MM/YYYY HH:mm')}
                  </span>
                </div>
              }
              description={
                <div>
                  {i18n.language === 'vi'
                    ? item.bodyVi
                    : i18n.language === 'en'
                      ? item.body
                      : item.bodyCn}
                </div>
              }
            />
          </List.Item>
        )}
      />
      <div style={{ textAlign: 'center', padding: 10 }}>
        <Button onClick={gotoProfile} type="link">
          {t('home.all')}
        </Button>
      </div>
    </div>
  );

  return (
    <Dropdown
      overlay={
        notifications?.length > 0 ? (
          menu
        ) : (
          <Flex
            justify="center"
            align="center"
            style={{
              ...notificationListsStyle,
              height: 100,
            }}
          >
            {t('header.empty_notice')}
          </Flex>
        )
      }
      open={isOpen}
      onOpenChange={handleOpenChange}
      trigger={['click']}
    >
      <div>
        <Badge count={numberNoticeState}>
          <Button
            icon={<Icons src="notice" size={20} />}
            style={{ color: '#fff', fontSize: 18 }}
            type="text"
          />
        </Badge>
      </div>
    </Dropdown>
  );
};

export default NotificationDropdown;
