import { FireOutlined, RightOutlined } from '@ant-design/icons';
import { Flex } from 'antd';
import { CSSProperties, FC, JSX, ReactNode } from 'react';
import IconCover from './IconWrapper';
import IconWrapper from './IconWrapper';
interface TopSectionProps {
  children: ReactNode;
  leftText: {
    text: string;
    style?: CSSProperties;
  };
  rightText?: {
    text: string;
    style?: CSSProperties;
  };
  wrapperStyles?: CSSProperties;
  showIcon?: boolean;
  icon?: ReactNode;
}

const TopSection: FC<TopSectionProps> = ({
  children,
  wrapperStyles,
  leftText,
  rightText,
  icon,
}) => {
  return (
    <section style={{ ...wrapperStyles }}>
      <Flex justify="space-between">
        <Flex gap={8} align="center">
          <IconWrapper style={leftText.style?.color} icon={icon} />
          <span style={{ ...leftText.style }}>{leftText.text}</span>
        </Flex>
        <Flex gap={5} align="center" style={{ ...rightText?.style }}>
          {rightText?.text && (
            <>
              <span>{rightText?.text}</span>
              <RightOutlined style={{ fontSize: 13 }} />
            </>
          )}
        </Flex>
      </Flex>
      {children}
    </section>
  );
};

export default TopSection;
