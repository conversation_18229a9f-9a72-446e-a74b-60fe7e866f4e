import React, { CSSProperties } from 'react';
import { Row, Col, Card, Flex } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import { t } from 'i18next';
import { useListProducts } from '@/views/search-menu/hooks/useListProducts';
import Slider from 'react-slick';
import ProductItem from '../product/ProductItem';
import ProductItemOverlay from '../product/ProductItemOverlay';

const colCardStyle: CSSProperties = {
  height: 80,
  width: '100%',
  backgroundColor: '#f5f5f5',
};
const ProductCards = () => {
  const { listTrending, listFlashSale } = useListProducts();

  const responsive = [
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 1280,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },

    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 800,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },

    {
      breakpoint: 700,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },
  ];

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 2,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    responsive,
    arrows: false,
  };

  return (
    <Row gutter={[8, 8]} style={{ marginTop: 16 }}>
      <Col span={12}>
        <div
          style={{
            borderRadius: 8,
            overflow: 'hidden',
            backgroundColor: '#fff',
          }}
        >
          <h4
            style={{
              fontWeight: 'bold',
              color: '#F37421',
              borderBottom: '1px solid #f0f0f0',
              background: '#fff',
              padding: 8,
              margin: 0,
            }}
          >
            {t('home.trending')} <RightOutlined />
          </h4>
          <div>
            <Slider {...settings}>
              {listTrending.map((product, index) => (
                <div style={{ background: 'white' }} key={index}>
                  <ProductItemOverlay item={{ ...product }} />
                </div>
              ))}
            </Slider>
          </div>
        </div>
      </Col>

      <Col span={12}>
        <div
          style={{
            borderRadius: 8,
            overflow: 'hidden',
            backgroundColor: '#fff',
          }}
        >
          <h4
            style={{
              fontWeight: 'bold',
              color: '#F37421',
              borderBottom: '1px solid #f0f0f0',
              background: '#fff',
              padding: 8,
              margin: 0,
            }}
          >
            {t('home.flashSale')} <RightOutlined />
          </h4>
          <div>
            <Slider {...settings}>
              {listFlashSale.map((product, index) => (
                <div style={{ background: 'white' }} key={index}>
                  <ProductItemOverlay item={{ ...product }} />
                </div>
              ))}
            </Slider>
          </div>
        </div>
      </Col>
    </Row>
  );
};

export default ProductCards;
