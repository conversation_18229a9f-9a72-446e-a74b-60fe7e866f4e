import { white } from '@/constants/themeColor';
import { Col, Flex, Grid } from 'antd';
import { CSSProperties, FC } from 'react';
import Slider from 'react-slick';
interface bannerProps {}

const bannerWrapper: CSSProperties = {
  height: '450px',
  borderRadius: 5,
  overflow: 'hidden',
  width: '100%',
  background: white,
};

const fullWidth: CSSProperties = {
  width: '100%',
  height: '100%',
};

const imgBaseSize: CSSProperties = {
  backgroundRepeat: 'no-repeat',
  backgroundSize: '100% 100%',
};

const leftSide: CSSProperties = {
  backgroundImage:
    "url('https://cdn.shoplucky.vn/files/8d5ed390926d4b2383a9a199537246fdWEBSPOTER111.png')",
  ...imgBaseSize,
};

const rightSideTop: CSSProperties = {
  backgroundImage:
    "url('https://cdn.shoplucky.vn/files/63c1e4282c6b499a9d2e799aaf01d71cposter2.png')",
  ...imgBaseSize,
  ...fullWidth,
};

const rightSideBottom: CSSProperties = {
  backgroundImage:
    "url('https://cdn.shoplucky.vn/files/38724e8ddfad4377ad13d8eaa69ac70aPOSTER_WEB333.png')",
  ...imgBaseSize,
  ...fullWidth,
};

// TODO: REMOVE URL HARDCODE

const Banner: FC<bannerProps> = ({}) => {
  const { xl } = Grid.useBreakpoint();
  const topSizeAtCol = xl ? {} : { height: 850 };
  const bottomSizeAtCol = xl ? {} : { height: 300 };

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2500,
    arrows: false,
  };

  return (
    <>
      {xl ? (
        <Flex
          vertical={!xl}
          gap={5}
          style={{ ...bannerWrapper, ...topSizeAtCol }}
        >
          <Col flex={3} style={leftSide}></Col>
          <Col flex={2} style={bottomSizeAtCol}>
            <Flex vertical gap={5} style={fullWidth}>
              <div
                style={{
                  ...rightSideTop,
                  ...bottomSizeAtCol,
                }}
              ></div>
              <div style={{ ...rightSideBottom, ...bottomSizeAtCol }}></div>
            </Flex>
          </Col>
        </Flex>
      ) : (
        <div>
          <Slider {...settings}>
            {[...new Array()].map((_, item) => (
              <img
                key={item}
                src="https://lucas.vn/wp-content/uploads/2022/11/Banner-chinh-hang-sieu-sale.jpg"
                alt="cover"
                loading="lazy"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  overflow: 'hidden',
                }}
              />
            ))}
          </Slider>
        </div>
      )}
    </>
  );
};

export default Banner;
