import { FC } from 'react';
import Slider from 'react-slick';
import TrendItem from './TrendItem';

const Trends: FC = () => {
  const responsive = [
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 1280,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },

    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 800,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
  ];

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    responsive,
    arrows: false
  };

  return (
    <Slider {...settings}>
      <div>
        <TrendItem
          childsBg={[
            'https://i5.walmartimages.com/seo/Total-by-Verizon-Apple-iPhone-12-64GB-Black-Prepaid-Smartphone-Locked-to-Total-by-Verizon_66b2853b-6cb5-4f20-b73a-b60b39b6de44.6b3bf83a920058a47342318925f1dc2b.jpeg',
            'https://www.phucanh.vn/media/news/1906_top-laptop-van-phong-gia-re-2024-1.jpg',
            'https://consumer.huawei.com/dam/content/dam/huawei-cbg-site/common/mkt/plp-x/tablet/autumn-2024-wearable-launch/matepad-series/huawei-matepad-11-5-s-grey.jpg',
          ]}
          name="Điện thoại"
          bg="#F3F9FF"
          count={2000}
        />
      </div>
      <div>
        <TrendItem
          childsBg={[
            'https://i5.walmartimages.com/seo/Total-by-Verizon-Apple-iPhone-12-64GB-Black-Prepaid-Smartphone-Locked-to-Total-by-Verizon_66b2853b-6cb5-4f20-b73a-b60b39b6de44.6b3bf83a920058a47342318925f1dc2b.jpeg',
            'https://www.phucanh.vn/media/news/1906_top-laptop-van-phong-gia-re-2024-1.jpg',
            'https://consumer.huawei.com/dam/content/dam/huawei-cbg-site/common/mkt/plp-x/tablet/autumn-2024-wearable-launch/matepad-series/huawei-matepad-11-5-s-grey.jpg',
          ]}
          name="Laptop"
          bg="#FFF7F3"
          count={1500}
        />
      </div>
      <div>
        <TrendItem
          childsBg={[
            'https://i5.walmartimages.com/seo/Total-by-Verizon-Apple-iPhone-12-64GB-Black-Prepaid-Smartphone-Locked-to-Total-by-Verizon_66b2853b-6cb5-4f20-b73a-b60b39b6de44.6b3bf83a920058a47342318925f1dc2b.jpeg',
            'https://www.phucanh.vn/media/news/1906_top-laptop-van-phong-gia-re-2024-1.jpg',
            'https://consumer.huawei.com/dam/content/dam/huawei-cbg-site/common/mkt/plp-x/tablet/autumn-2024-wearable-launch/matepad-series/huawei-matepad-11-5-s-grey.jpg',
          ]}
          name="Xe máy"
          bg="#FCF3FF "
          count={200}
        />
      </div>
      <div>
        <TrendItem
          childsBg={[
            'https://i5.walmartimages.com/seo/Total-by-Verizon-Apple-iPhone-12-64GB-Black-Prepaid-Smartphone-Locked-to-Total-by-Verizon_66b2853b-6cb5-4f20-b73a-b60b39b6de44.6b3bf83a920058a47342318925f1dc2b.jpeg',
            'https://www.phucanh.vn/media/news/1906_top-laptop-van-phong-gia-re-2024-1.jpg',
            'https://consumer.huawei.com/dam/content/dam/huawei-cbg-site/common/mkt/plp-x/tablet/autumn-2024-wearable-launch/matepad-series/huawei-matepad-11-5-s-grey.jpg',
          ]}
          name="Túi xách"
          bg="#F3FFF4"
          count={300}
        />
      </div>
    </Slider>
  );
};

export default Trends;
