import { bgColor } from '@/constants/themeColor';
import { Flex } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';

const trendItem: CSSProperties = {
  maxWidth: '98%',
  background: 'white',
  border: `1px solid ${bgColor}`,
  borderRadius: 8,
};
const width: CSSProperties = {
  width: '33%',
};

interface ITrendItemProps {
  bg: string;
  name: string;
  count: number;
  childsBg: string[];
}

const TrendItem: FC<ITrendItemProps> = ({ bg, name, count, childsBg }) => {
  const { t } = useTranslation();
  return (
    <div style={trendItem}>
      <Flex align="center" vertical>
        <Flex gap={5} align="center" style={{ padding: 10 }}>
          {childsBg?.map((item, index) => (
            <img
              key={index}
              style={width}
              src={item}
              alt="Cover"
              loading="lazy"
            />
          ))}
        </Flex>
      </Flex>
      <Flex vertical style={{ background: bg, padding: 10 }}>
        <span className="text-title">{name}</span>
        <span className="text-count">
          {count} {t('home.product')}
        </span>
      </Flex>
    </div>
  );
};

export default TrendItem;
