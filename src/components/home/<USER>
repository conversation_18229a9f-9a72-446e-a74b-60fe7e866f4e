import { Flex } from 'antd';
import React, { useEffect, useState } from 'react';

interface CountdownTimerProps {
  timeLeft: number; 
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ timeLeft }) => {
  const [remainingTime, setRemainingTime] = useState(timeLeft);

  useEffect(() => {
    const timer = setInterval(() => {
      setRemainingTime((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    return () => clearInterval(timer); 
  }, []);

  
  const formatTime = (time: number) => ({
    hours: Math.floor((time / 3600) % 24),
    minutes: Math.floor((time / 60) % 60),
    seconds: Math.floor(time % 60),
  });

  const { hours, minutes, seconds } = formatTime(remainingTime);

  return (
    <div>
      <Flex
        justify="center"
        align="center"
        gap={5}
        style={{
          background: '#FF8181',
          display: 'inline-flex',
          padding: '6px 8px',
        }}
      >
        <span style={timeBoxStyle}>{String(hours).padStart(2, '0')}</span>
        <span style={splitDot}>:</span>
        <span style={timeBoxStyle}>{String(minutes).padStart(2, '0')}</span>
        <span style={splitDot}>:</span>
        <span style={timeBoxStyle}>{String(seconds).padStart(2, '0')}</span>
      </Flex>
    </div>
  );
};

const timeBoxStyle: React.CSSProperties = {
  backgroundColor: '#F64C26',
  padding: '5px',
  color: 'white',
  fontSize: 15,
  fontWeight: 400,
};

const splitDot: React.CSSProperties = {
  color: 'white',
  fontSize: 15,
  fontWeight: 400,
};

export default CountdownTimer;
