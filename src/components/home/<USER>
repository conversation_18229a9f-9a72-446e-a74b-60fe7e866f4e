import { redColor, textColorBase } from '@/constants/themeColor';
import { formatVND } from '@/utils/numberFormat';
import { Flex, Tooltip } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';
import CountdownTimer from './CountdownTimer';
import { LatestRevealRes } from '@/dto/product.dto';
import BadgeWinner from './BadgeWinner';
import { useContentLang } from '@/hooks/useContentLang';

const productItemWrapper: CSSProperties = {
  padding: '0px 5px',
  position: 'relative',
  // boxShadow: ' rgba(0, 0, 0, 0.05) 0px 5px 10px 0px',
  maxWidth: '98%',
  background: 'white',
  borderRadius: 6,
  marginBottom: 5,
};

const productImgStyles: CSSProperties = {
  width: 50,
  height: 50,
  backgroundPosition: 'center',
  backgroundSize: '100% 100%',
};

const productNameStyles: CSSProperties = {
  fontWeight: 600,
  fontSize: 14,
  color: textColorBase,
  lineHeight: '17px',
  display: '-webkit-box',
  WebkitLineClamp: 1,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  minHeight: 20,
  cursor: 'pointer',
};

const trendTitle: CSSProperties = {
  color: '#616161',
  fontWeight: 400,
  fontSize: 13,
};

const price: CSSProperties = {
  color: redColor,
  fontSize: 18,
  fontWeight: 700,
};

const winnerName: CSSProperties = {
  color: redColor,
  fontWeight: 500,
  fontSize: 13,
};

const trendBottomItems: CSSProperties = {
  width: '100%',
  minHeight: 50,
};

const RevealItem: FC<{ item: LatestRevealRes }> = ({ item }) => {
  const { t } = useTranslation();
  const name = useContentLang(item.product);
  return (
    <div style={productItemWrapper}>
      <Flex align="center" gap={10}>
        <img
          src={item?.product?.cover}
          alt={item?.product?.name}
          style={productImgStyles}
        />
        <div>
          <span className="product-name" style={productNameStyles}>
            {item?.winner?.firstName}
          </span>
          <span
            style={{
              color: redColor,
              fontSize: 13,
              fontWeight: 500,
              display: 'inline-block',
              padding: '0 3px',
            }}
          >
            {item?.winningTicketNumber}
          </span>
        </div>
      </Flex>
      
    </div>
  );
};

export default RevealItem;
