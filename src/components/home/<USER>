import { Col, Grid, Row } from 'antd';
import Slider from 'react-slick';
import { CSSProperties } from 'react';
import SimpleCategory from './SimpleCategory';
import { useTranslation } from 'react-i18next';
import { CategoryDto } from '@/dto/category.dto';
import { useFilters } from '@/views/search-menu/hooks/useFilters';
import '../common/CustomScroll.css';

const categoriesBySvgWrapper: CSSProperties = {
  padding: '10px 0',
  paddingBottom: 10,
  // backgroundColor: '#ffffff',
  borderBottomLeftRadius: 8,
  borderBottomRightRadius: 8,
};

export interface ICategoriesBySvgProps {
  listCategory: CategoryDto[];
  type?: string;
}

const responsive = [
  {
    breakpoint: 1440,
    settings: {
      slidesToShow: 8,
      slidesToScroll: 8,
    },
  },
  {
    breakpoint: 1280,
    settings: {
      slidesToShow: 8,
      slidesToScroll: 8,
    },
  },

  {
    breakpoint: 1024,
    settings: {
      slidesToShow: 4,
      slidesToScroll: 4,
    },
  },
  {
    breakpoint: 800,
    settings: {
      slidesToShow: 3,
      slidesToScroll: 3,
    },
  },

  {
    breakpoint: 700,
    settings: {
      slidesToShow: 2,
      slidesToScroll: 2,
    },
  },
];

const settings = {
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 8,
  slidesToScroll: 8,
  autoplay: true,
  autoplaySpeed: 2500,
  responsive,
  // arrows: false,
};

const CategoriesBySvg = ({ listCategory, type }: ICategoriesBySvgProps) => {
  const { t } = useTranslation();
  const { handleCategoryChange, selectedCategoryId } = useFilters();
  const { md } = Grid.useBreakpoint();
  return md ? (
    <div
      style={{
        ...categoriesBySvgWrapper,
      }}
    >
      <Slider {...settings}>
        {listCategory.map((info, idx) => (
          <div
            key={idx}
            onClick={() =>
              handleCategoryChange(
                info.id,
                (type = type === 'voucher' ? 'voucher' : 'product')
              )
            }
          >
            <SimpleCategory
              {...info}
              isSelected={selectedCategoryId === info.id}
            />
          </div>
        ))}
      </Slider>
    </div>
  ) : (
    <Row
      className="custom-scroll"
      style={{
        ...categoriesBySvgWrapper,
        flexWrap: 'nowrap',
        overflowX: 'auto',
        paddingBottom: '8px',
      }}
    >
      {listCategory.map((info, index) => (
        <Col
          key={index}
          style={{
            flex: '0 0 20%',
            maxWidth: '20%',
            cursor: 'pointer',
          }}
          onClick={() =>
            handleCategoryChange(
              info.id,
              (type = type === 'voucher' ? 'voucher' : 'product')
            )
          }
        >
          <SimpleCategory
            {...info}
            isSelected={selectedCategoryId === info.id}
          />
        </Col>
      ))}
    </Row>
  );
};

export default CategoriesBySvg;
