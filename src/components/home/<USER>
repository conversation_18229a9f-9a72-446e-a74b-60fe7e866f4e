import { CategoryDto } from '@/dto/category.dto';
import { useContentLang } from '@/hooks/useContentLang';
import { Flex, Grid } from 'antd';
import { CSSProperties, FC } from 'react';
// interface ISimpleCategoryProps {
//   cover: string;
//   name: string;
//   nameVi?: string;
//   nameCn?: string;

// }
interface ISimpleCategoryProps extends CategoryDto {
  isSelected?: boolean;
}

const baseStyle: CSSProperties = {
  width: 79,
  height: 79,
};

const mobileBaseStyle: CSSProperties = {
  width: 50,
  height: 50,
  borderRadius: 5,
  background: '#ffffff',
};

const categoryStyle: CSSProperties = {
  textAlign: 'center',
  // width: 100,
  // color: '#212121',
  fontWeight: 400,
  cursor: 'pointer',
};

const coverStyle: CSSProperties = {
  width: '100%',
  height: '100%',
  objectFit: 'contain',
  boxShadow: '8px 8px 12px 0px #0000001A',
  borderRadius: 5,
};

const SimpleCategory = ({ isSelected, ...item }: ISimpleCategoryProps) => {
  const { md } = Grid.useBreakpoint();
  const name = useContentLang(item);
  const wrapperStyle: CSSProperties = {
    ...(!md ? mobileBaseStyle : baseStyle),
  };

  return (
    <Flex
      vertical
      justify="center"
      align="center"
      gap={10}
      style={{ width: '100%', borderRadius: 5 }}
    >
      <div style={wrapperStyle}>
        <img
          style={{ ...coverStyle }}
          src={item?.image}
          alt="cover"
          loading="lazy"
        />
      </div>
      <div
        style={{
          ...categoryStyle,
          fontSize: md ? 15 : 12,
          // color: isSelected ? '#000000' : '#ffffff',
          // color: isSelected ? '#000000' : '#ffffff',
          color: md ? '#000000' : '#ffffff',
        }}
      >
        {name}
      </div>
    </Flex>
  );
};

export default SimpleCategory;
