import { primaryColor, white } from '@/constants/themeColor';
import { Flex } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';

const trendItemWrapper: CSSProperties = {
  borderRadius: 8,
  padding: 10,
  border: '1px solid #F5F5F5',
  background: white,
  width: '100%',
  height: '100%',
};

const cover: CSSProperties = {
  width: '100%',
  height: '150px',
  objectFit: 'cover',
  background: white,
};

const trendTitle: CSSProperties = {
  color: primaryColor,
  fontWeight: 'bold',
  fontSize: 15,
};

const quantity: CSSProperties = {
  color: '#9E9E9E',
  fontSize: 13,
};
const trendBottomItems: CSSProperties = {
  width: '100%',
  minHeight: 50,
};

interface ICategoryItemProps {
  cover: string;
  title: string;
  quantity: number;
}

const CategoryItem: FC<{ item: ICategoryItemProps }> = ({ item }) => {
  const { t } = useTranslation();

  return (
    <div style={trendItemWrapper}>
      <Flex vertical align="center" justify="space-between" gap={10}>
        <img
          loading="lazy"
          className="product-cover"
          style={cover}
          src={item.cover}
          alt={item.title}
        />
        <Flex style={trendBottomItems} gap={5} vertical>
          <span style={trendTitle}>{item.title}</span>
          <span style={quantity}>
            {item.quantity} {t('home.product')}
          </span>
        </Flex>
      </Flex>
    </div>
  );
};

export default CategoryItem;
