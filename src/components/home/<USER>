import { white } from '@/constants/themeColor';
import { Flex } from 'antd';
import { CSSProperties, FC } from 'react';

const itemWrapper: CSSProperties = {
  background: white,
  borderRadius: 5,
  padding: 10,
  minHeight: 145,
};

interface ICouponItemProps {
  cover: string;
  text: string;
}

const CouponItem: FC<{ item: ICouponItemProps }> = ({ item }) => {
  return (
    <Flex vertical style={itemWrapper} align="center" justify="center" gap={5}>
      <img loading="lazy" src={item.cover} alt={item.text} />
      <span style={{ textAlign: 'center', height: 37 }}>{item.text}</span>
    </Flex>
  );
};

export default CouponItem;
