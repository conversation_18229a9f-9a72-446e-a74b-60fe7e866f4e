import { Grid } from 'antd';
import { CSSProperties, FC, ReactNode } from 'react';
interface ContainerProps {
  children: ReactNode;
  style?: CSSProperties;
}

const Container: FC<ContainerProps> = ({ children, style }) => {
  const { md } = Grid.useBreakpoint();
  return (
    <section style={{ ...style, padding: md ? '0 8vw' : 15 }}>
      {children}
    </section>
  );
};

export default Container;
