import { white } from '@/constants/themeColor';
import { Col, Flex, Grid, Row } from 'antd';
import { CSSProperties, FC } from 'react';
import { Carousel } from 'antd';
import Slider from 'react-slick';
import '../../assets/styles/_banner-home.scss';

interface bannerProps {}

const colImgStyle: CSSProperties = {
  borderRadius: 10,
  // backgroundColor: white,
};

const settings = {
  dots: false,
  infinite: true,
  autoplay: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
};

const BannerMob: FC<bannerProps> = ({}) => {
  const { md } = Grid.useBreakpoint();
  const bannerCenter = [
    'https://cdn.shoplucky.vn/files/6dbc53585447437fa962afa409c2836cPOSTERArtboard2.jpg',
    'https://cdn.shoplucky.vn/files/2355c8de2b5d4ec58fc8d2b5df9041efbannerPCArtboard01.jpg',
    'https://cdn.shoplucky.vn/files/ff64e2d5536b49fbabecdc7d3556c29aposter_2Mobile_.png',
  ];
  return (
    <Row gutter={10}>
      <Col
        span={6}
        style={{
          ...colImgStyle,
        }}
      >
        <img
          src="https://cdn.shoplucky.vn/files/60d4e7b50b1e4a97a019563de72c2b7dbanner01.jpg"
          style={{
            borderRadius: 10,
            width: '100%',
          }}
        />
      </Col>
      <Col
        className="slider-container"
        span={12}
        style={{
          ...colImgStyle,
        }}
      >
        {bannerCenter.length > 1 ? (
          <Slider {...settings}>
            {bannerCenter.map((item, idx) => (
              <div key={idx}>
                <img
                  src={item}
                  style={{ borderRadius: 10, maxHeight: 400, width: '100%' }}
                />
              </div>
            ))}
          </Slider>
        ) : (
          <img
            src={bannerCenter[0]}
            style={{ borderRadius: 10, maxHeight: 400, width: '100%' }}
          />
        )}
      </Col>
      <Col
        span={6}
        style={{
          ...colImgStyle,
        }}
      >
        <img
          src="https://cdn.shoplucky.vn/files/9ff11c52d01242aebe264e2ca837fb6ePOSTERArtboard3.jpg"
          style={{ borderRadius: 10, width: '100%' }}
        />
      </Col>
    </Row>
  );
};

export default BannerMob;
