import {
  redColor,
  textColorBase,
  yellowGradient,
} from '@/constants/themeColor';
import { ComingSoonRes } from '@/dto/product.dto';
import { ROUTES } from '@/enums/Routes';
import { useContentLang } from '@/hooks/useContentLang';
import { formatNumber } from '@/utils/numberFormat';
import { Flex, Grid, Progress, Tooltip } from 'antd';
import { CSSProperties, FC, startTransition } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, useNavigate } from 'react-router-dom';

const productItemWrapper: CSSProperties = {
  padding: '0px 5px',
  boxShadow: 'rgba(0, 0, 0, 0.04) 0px 3px 3px',
  maxWidth: '95%',
  background: 'white',
  borderRadius: 8,
  marginBottom: 0,
};

const productNameStyles: CSSProperties = {
  fontSize: '1em',
  color: textColorBase,
  fontWeight: 400,
  display: '-webkit-box',
  WebkitLineClamp: 1,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  minHeight: 20,
  cursor: 'pointer',
};

const remain: CSSProperties = {
  color: '#616161',
  fontWeight: 400,
  fontSize: 13,
};

const ComingSoonItem: FC<{ item: ComingSoonRes }> = ({ item }) => {
  const { t } = useTranslation();
  const { md } = Grid.useBreakpoint();
  const navigate = useNavigate();
  const gotoDetail = () => {
    startTransition(() => {
      const baseUrl = generatePath(ROUTES.PRODUCT_DETAILS, {
        id: item.productId.toString(),
      });
      const url = `${baseUrl}?type=voucher`;
      navigate(url);
    });
  };
  const name = useContentLang(item.product);
  return (
    <div style={productItemWrapper} onClick={gotoDetail}>
      <Flex align="center" gap={10}>
        <div
          style={{
            backgroundImage: `url(${item?.product?.cover})`,
            backgroundSize: '100% 100%',
            flex: 1,
            width: 25,
            height: !md ? 50 : 65,
          }}
        />
        <Flex vertical style={{ width: '55%' }}>
          <Tooltip title={name}>
            <span style={productNameStyles}>{name}</span>
          </Tooltip>
          {/* <Progress
            strokeColor={yellowGradient}
            percent={70}
            showInfo={false}
            status="active"
          /> */}
          <div style={remain}>
            <span
              style={{
                color: redColor,
                fontWeight: 500,
                display: 'inline-block',
                padding: '0 3px',
              }}
            >
              {`${item.soldTickets} / ${item.totalTickets} ${t('home.ticket')}`}
            </span>
          </div>
        </Flex>
      </Flex>
    </div>
  );
};

export default ComingSoonItem;
