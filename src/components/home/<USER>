import { Card, Row, Col, Badge } from 'antd';
import {
  WalletOutlined,
  ShoppingCartOutlined,
  GiftOutlined,
} from '@ant-design/icons';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/enums/Routes';
import { t } from 'i18next';
import { Icons } from '..';

const PromoSection = () => {
  const { cart, startTransition } = useGlobalContext();
  const navigate = useNavigate();

  const onNavigate = (url: string) => {
    startTransition(() => {
      navigate(url);
    });
  };
  const items = [
    {
      icon: <Icons src="wallet" size={15} />,
      title: t('header.personal_wallet'),
      description: t('header.checkNow'),
      onClick: onNavigate.bind(null, '/profile/4'),
    },
    {
      icon: (
        <Badge count={cart.length} offset={[0, -5]} style={{ top: 3 , left: 65}}>
          <Icons src="cart-red" size={15} />
        </Badge>
      ),
      title: t('header.cart'),
      description: t('header.checkNow'),
      onClick: onNavigate.bind(null, ROUTES.PRODUCT_CART),
    },
    {
      icon: <Icons src="voucher-red" size={15} />,
      title: t('header.voucher'),
      description: t('header.getNow'),
      onClick: onNavigate.bind(null, '/profile/6'),
    },
  ];

  return (
    <Row
      justify="space-between"
      align="middle"
      style={{
        borderRadius: '10px',
        background: '#f5f5f5',
        padding: '10px 0',
        margin: '10px 0',
      }}
    >
      {items.map((item, index) => (
        <Col
          key={index}
          span={8}
          style={{
            borderRight:
              index !== items.length - 1 ? '1px solid #f5d3c0' : 'none',
            padding: '0 12px',
          }}
        >
          <div onClick={item.onClick}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'start',
                // marginBottom: '4px',
              }}
            >
              <div
                style={{
                  fontSize: '15px',
                  color: '#f94c43',
                  marginRight: '3px',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {item.icon}
              </div>
              <div style={{ fontWeight: 400, fontSize: '13px', color: '#000' }}>
                {item.title}
              </div>
            </div>
            <div style={{ color: '#f94c43', fontSize: '10px' }}>
              {item.description}
            </div>
          </div>
        </Col>
      ))}
    </Row>
  );
};

export default PromoSection;
