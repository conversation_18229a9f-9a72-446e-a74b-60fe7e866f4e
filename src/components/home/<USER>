import { Flex, Grid } from 'antd';
import { CSSProperties, FC, ReactNode } from 'react';

interface IIconWrapperProps {
  icon: ReactNode;
  style?: string;
}

const wrapperIcon: CSSProperties = {
  borderRadius: '100%',
};

const convertHexToRGBA = (hex: string, opacity: number) => {
  hex = hex.replace('#', '');
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

const IconWrapper: FC<IIconWrapperProps> = ({ icon, style }) => {
  const { xl } = Grid.useBreakpoint();
  const opacity = 0.1;
  const backgroundColor = convertHexToRGBA(style || '', opacity);

  return (
    <>
      <Flex
        justify="center"
        align="center"
        style={{
          ...wrapperIcon,
          color: style,
          backgroundColor,
          width: xl ? 46 : 35,
          height: xl ? 46 : 35,
          fontSize: xl ? 24 : 18,
        }}
      >
        {icon}
      </Flex>
    </>
  );
};

export default IconWrapper;
