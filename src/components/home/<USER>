import { Flex } from 'antd';
import React, { useEffect, useState } from 'react';



const BadgeWinner = ({ winningTicketNumber }: { winningTicketNumber: string }) => {
  return (
    <div>
      <Flex
        justify="center"
        align="center"
        gap={5}
        style={{
          background: '#FF8181',
          display: 'inline-flex',
          padding: '6px 8px',
        }}
      >
        <span style={timeBoxStyle}>{winningTicketNumber}</span>
      </Flex>
    </div>
  );
};

const timeBoxStyle: React.CSSProperties = {
  backgroundColor: '#F64C26',
  padding: '5px',
  color: 'white',
  fontSize: 15,
  fontWeight: 400,
};

const splitDot: React.CSSProperties = {
  color: 'white',
  fontSize: 15,
  fontWeight: 400,
};

export default BadgeWinner;
