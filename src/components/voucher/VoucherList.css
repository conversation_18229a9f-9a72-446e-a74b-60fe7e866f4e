.voucher-container {
  overflow: hidden;
  width: 100%;
  position: relative;
}

.voucher-scrolling-wrapper {
  display: flex;
  width: max-content;
  animation: scrollLeft 30s linear infinite;
}

.voucher-item {
  flex: 0 0 auto;
  min-width: 140px;
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
}

.voucher-image {
  background: #F37421;
  flex-shrink: 0;
}

@keyframes scrollLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}
  