import React from 'react';
import { Typography, Row, Col, Image, Card, Grid } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import '../common/CustomScroll.css';
import { t } from 'i18next';
import './VoucherList.css';
import ComingSoon from '@/views/home/<USER>';

const { Title, Text } = Typography;

export const VoucherList = () => {
  const { md } = Grid.useBreakpoint();
  return (
    <Card
      style={{
        borderRadius: 10,
        color: '#fff',
        marginTop: 16,
        overflow: 'hidden',
      }}
      bodyStyle={{ padding: 5 }}
    >
      <div
        style={{
          padding: '1px 8px',
          background: 'white',
          display: 'inline-block',
          borderBottomRightRadius: 20,
        }}
      >
        <Title
          level={5}
          style={{
            color: '#F37421',
            margin: 0,
            display: 'inlineBlock',
            paddingBottom: 5,
          }}
        >
          {t('voucher.voucher_coming_soon')} <RightOutlined />
        </Title>
      </div>

      <div className="voucher-container">
        <ComingSoon />
      </div>
    </Card>
  );
};
