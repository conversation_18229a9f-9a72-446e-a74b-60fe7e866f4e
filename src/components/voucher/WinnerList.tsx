import { Card, Grid, Typography } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import Text from '../common/Text';
import { t } from 'i18next';
import './WinnersList.css';
import LatestReveal from '@/views/home/<USER>';

const { Title } = Typography;

export const WinnersList = () => {
  const { md } = Grid.useBreakpoint();

  return (
    <Card
      style={{
        borderRadius: 10,
        color: '#fff',
        marginTop: 16,
        overflow: 'hidden',
      }}
      bodyStyle={{ padding: 5 }}
    >
      <div
        style={{
          padding: '0 8px',
          background: 'white',
          display: 'inline-block',
          borderBottomRightRadius: 20,
        }}
      >
        <Title
          level={5}
          style={{
            color: '#F37421',
            margin: 0,
            display: 'inlineBlock',
            paddingBottom: 5,
          }}
        >
          {t('voucher.list_winners')} <RightOutlined />
        </Title>
      </div>

      <div className="winners-container">
        {/* <div className="scrolling-wrapper"> */}
        {/* {winners.map((name, idx) => (
            <div key={idx} className="winner-item">
              <Text style={{ fontSize: 14, padding: md ? '10px 0' : 0, color: '#000000' }}>{name}</Text>
            </div>
          ))} */}
        <LatestReveal />
        {/* </div> */}
      </div>
    </Card>
  );
};
