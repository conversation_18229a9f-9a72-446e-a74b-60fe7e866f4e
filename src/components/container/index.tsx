import { Layout } from 'antd';
import { CSSProperties, FC, ReactNode } from 'react';

interface ContainerProps {
  hidden?: boolean;
  style?: CSSProperties;
  children?: ReactNode;
}

const Container: FC<ContainerProps> = ({ children, style, hidden }) => {
  return (
    !hidden && (
      <Layout
        style={{
          padding: 20,
          borderRadius: 8,
          background: '#fff',
          position: 'relative',
          ...style,
        }}
      >
        {children}
      </Layout>
    )
  );
};

export default Container;
