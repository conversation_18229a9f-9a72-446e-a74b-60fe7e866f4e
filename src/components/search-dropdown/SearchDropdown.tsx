import { useState, useEffect, useRef } from 'react';
import {
  Input,
  Dropdown,
  Spin,
  message,
  Menu,
  Typography,
  Grid,
  Button,
  Tag,
  Space,
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useSearchDropDown } from './hooks/useSearchDropdown';
import { t } from 'i18next';
import { generatePath, Link } from 'react-router-dom';
import { ROUTES } from '@/enums/Routes';
import { useListProducts } from '@/views/search-menu/hooks/useListProducts';

const SearchDropdown = () => {
  const {
    data: listProducts,
    loading,
    searchText,
    handleIconClick,
    handleItemClick,
    setSearchText,
  } = useSearchDropDown();

  const { listTrending } = useListProducts();
  const { md } = Grid.useBreakpoint();
  const [open, setOpen] = useState(false);
  const [currentTrendIndex, setCurrentTrendIndex] = useState(0);
  const [placeholder, setPlaceholder] = useState(t('home.searchPlaceholder'));
  const [showTrending, setShowTrending] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Rotate through trending search terms every 2 seconds
  useEffect(() => {
    if (listTrending && listTrending.length > 0) {
      // Initial placeholder
      setPlaceholder(`${listTrending[0]?.name || t('home.searchPlaceholder')}`);

      // Set up the interval
      intervalRef.current = setInterval(() => {
        setCurrentTrendIndex((prevIndex) => {
          const nextIndex = (prevIndex + 1) % listTrending.length;
          setPlaceholder(
            `${listTrending[nextIndex]?.name || t('home.searchPlaceholder')}`
          );
          return nextIndex;
        });
      }, 2000);
    }

    // Clean up interval on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [listTrending, t]);

  const handleSelectItem = (item) => {
    handleItemClick(item);
    setOpen(false);
  };

  const handleTrendingClick = (trendingItem) => {
    setSearchText(trendingItem.name);
    handleIconClick();
    setShowTrending(false);
  };

  const dropdownContent = (
    <Menu onClick={() => setOpen(false)}>
      {listProducts?.map((item, idx) => (
        <Menu.Item key={idx}>
          <Link
            to={generatePath(ROUTES.PRODUCT_DETAILS, {
              id: item.id.toString(),
            })}
            style={{ textDecoration: 'none', color: 'inherit' }}
            onClick={() => handleSelectItem(item)}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: 10 }}>
              <img
                src={item.cover}
                alt={item.name}
                style={{ width: 40, height: 40, borderRadius: 5 }}
              />
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <Typography.Text style={{ fontWeight: 'bold' }}>
                  {item.name}
                </Typography.Text>
                <Typography.Text style={{ color: 'gray' }}>
                  {item.buyNowPrice}
                </Typography.Text>
              </div>
            </div>
          </Link>
        </Menu.Item>
      ))}
    </Menu>
  );

  const trendingContent = (
    <div
      style={{
        padding: '10px',
        background: '#fff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
        borderRadius: '0 0 4px 4px',
        maxWidth: md ? 670 : '92%',
        position: 'absolute',
      }}
    >
      {/* <Typography.Text
        style={{ display: 'block', marginBottom: 10, color: '#666' }}
      >
        {t('search.trending')}
      </Typography.Text> */}
      <Space size={[8, 8]} wrap>
        {listTrending?.map((item, index) => (
          <Tag
            key={index}
            color="#f3731d"
            style={{
              cursor: 'pointer',
              padding: '4px 8px',
              borderRadius: '16px',
              textWrap: 'revert',
              margin: 0,
            }}
            onClick={() => handleTrendingClick(item)}
          >
            {item.name}
          </Tag>
        ))}
      </Space>
    </div>
  );

  return (
    <div style={{ width: md ? 670 : '100%' }}>
      <Dropdown
        overlay={dropdownContent}
        open={open && searchText.length >= 3 && listProducts.length > 0}
        placement="bottomLeft"
        onOpenChange={setOpen}
      >
        {md ? (
          <Input
            placeholder={placeholder}
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
              setOpen(true);
              setShowTrending(false);
            }}
            onBlur={() =>
              setTimeout(() => {
                setOpen(false);
                setShowTrending(false);
              }, 200)
            }
            onFocus={() => {
              // When user focuses on input, stop rotating placeholders and show trending
              if (intervalRef.current) {
                clearInterval(intervalRef.current);
              }
              setShowTrending(true);
            }}
            suffix={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                {loading && <Spin size="small" />}
                <Button
                  style={{
                    backgroundColor: '#F3731D',
                    color: '#fff',
                    border: 'none',
                    width: 50,
                  }}
                  icon={<SearchOutlined />}
                  onClick={handleIconClick}
                />
              </div>
            }
          />
        ) : (
          <Input
            placeholder={placeholder}
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
              setOpen(true);
              setShowTrending(false);
            }}
            onBlur={() =>
              setTimeout(() => {
                setOpen(false);
                setShowTrending(false);
              }, 200)
            }
            onFocus={() => {
              // When user focuses on input, stop rotating placeholders and show trending
              if (intervalRef.current) {
                clearInterval(intervalRef.current);
              }
              setShowTrending(true);
            }}
            prefix={
              <SearchOutlined
                style={{ color: 'gray', cursor: 'pointer' }}
                onClick={handleIconClick}
              />
            }
            suffix={loading && <Spin size="small" />}
          />
        )}
      </Dropdown>
      {showTrending &&
        listTrending &&
        listTrending.length > 0 &&
        searchText.length < 3 &&
        trendingContent}
    </div>
  );
};

export default SearchDropdown;
