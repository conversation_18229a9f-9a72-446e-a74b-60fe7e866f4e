import { ListProductsReq, ProductsDto } from '@/dto/product.dto';
import { ROUTES } from '@/enums/Routes';
import productsService from '@/services/products.service';
import { useListProducts } from '@/views/search-menu/hooks/useListProducts';
import { message } from 'antd';
import { startTransition, useCallback, useEffect, useState } from 'react';
import { generatePath, useNavigate } from 'react-router-dom';

export const useSearchDropDown = () => {
  const [data, setData] = useState<ProductsDto[]>();
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(false);
  // const { data: listProducts } = useListProducts();

  const listProducts = useCallback(async () => {
    try {
      const res = await productsService.list({
        pageIndex: 1,
        pageSize: 12,
        keyword: searchText,
      });
      setData(res?.data || []);
    } catch (error) {}
  }, [searchText]);

  useEffect(() => {
    listProducts();
  }, [listProducts]);

  // const handleSearch = (value: string) => {
  //   setSearchText(value);

  //   // Bắt đầu loading
  //   setLoading(true);

  //   if (value.length > 3) {
  //     // console.log('listProducts', listProducts);
  //     //   setFilteredProducts([]);
  //     //   setLoading(false);
  //     //   return;
  //   }
  //   setLoading(false);
  // };

  const handleIconClick = () => {
    if (searchText.length < 3) {
      message.warning('Vui lòng nhập ít nhất 3 ký tự để tìm kiếm!');
      return;
    }
    message.info(`Đang tìm kiếm với từ khóa: "${searchText}"`);
    console.log('Search triggered:', searchText);
    // handleSearch(searchText); // Gọi tìm kiếm
  };

  const handleItemClick = (product) => {
    message.info(`Bạn đã chọn: ${product.name}`);
    console.log('Selected product:', product);
  };

  return {
    data,
    loading,
    searchText,
    // handleSearch,
    handleIconClick,
    handleItemClick,
    setSearchText,
  };
};
