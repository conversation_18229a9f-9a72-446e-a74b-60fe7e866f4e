import { InputNumber } from 'antd';
import { CSSProperties, FC, ReactElement, useState } from 'react';
import './CartInputNumber.css'; // Include your custom styles

interface QuantityInputProps {
  min?: number;
  max?: number;
  style?: CSSProperties;
  defaultValue?: number;
  isMobileSize?: boolean;
  deleteFnc?: Function;
  isAddon?: boolean;
  onChange?: (value: number) => void;
  disabled?: boolean;
}

const QuantityInput: FC<QuantityInputProps> = ({
  min = 1,
  max = 99,
  defaultValue = min,
  onChange = () => {},
  isAddon = true,
  isMobileSize = false,
  deleteFnc = undefined,
  ...rest
}) => {
  const [quantity, setQuantity] = useState<number>(defaultValue);

  const handleChange = (value?: number) => {
    if (!value) {
      return;
    }
    const num = Math.min(Math.max(value ?? min, min), max);
    setQuantity(num);
    onChange(num);
  };

  const handleBefore = (quantity: number) => {
    if (quantity === 1 && deleteFnc) {
      deleteFnc();
      return 0;
    }
    return quantity - 1
  }

  const addon = (isBefore?: boolean): ReactElement => {
    return (
      <div
        style={{
          cursor: 'pointer',
          padding: '0',
          userSelect: 'none' /* Prevents text selection */,
          WebkitUserSelect: 'none' /* Safari */,
          MozUserSelect: 'none' /* Firefox */,
          msUserSelect: 'none' /* Internet Explorer/Edge */,
        }}
        onClick={() => handleChange(isBefore ? handleBefore(quantity) : quantity + 1)}
        dangerouslySetInnerHTML={{ __html: isBefore ? '-' : '+' }}
      />
    );
  };

  return (
    <InputNumber
      min={min}
      max={max}
      {...rest}
      controls={false}
      addonBefore={isAddon ? addon(true) : undefined}
      addonAfter={isAddon ? addon() : undefined}
      onChange={handleChange}
      value={quantity}
      size="small"
      className={
        isMobileSize
          ? 'custom-input-number mobile-screen'
          : 'custom-input-number'
      }
    />
  );
};

export default QuantityInput;
