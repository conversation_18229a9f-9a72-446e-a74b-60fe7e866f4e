.custom-input-number:not(.mobile-screen) {
    font-size: 14px;
    width: 70px;
}

.custom-input-number {
    font-size: 10px;
    width: 43px;
}

.custom-input-number:not(.mobile-screen) .ant-input-number-group-addon {
    padding: 0 5px;
    font-size: 14px;
}

.custom-input-number .ant-input-number-group-addon {
    border: none;
    padding: 0 3px;
    font-size: 10px;
}

.custom-input-number:not(.mobile-screen) .ant-input-number-input-wrap input {
    padding: 0 5px;
    text-align: center;
    font-size: 20px;
    color: #F37421;
}

.custom-input-number .ant-input-number-input-wrap input {
    padding: 0 2px;
    text-align: center;
    font-size: 10px;
}

.custom-input-number.ant-input-number-disabled .ant-input-number-input-wrap, 
 .ant-input-number-input-wrap:hover, 
 .ant-input-number-input-wrap:focus {
    background-color: #fff;
    color: rgba(0,0,0,1);
}

.ant-input-number.ant-input-number-sm.css-dev-only-do-not-override-qmu5gx.ant-input-number-outlined {
    border: none !important;
}