function formatNumber(num: number) {
  if (num >= 1000 && num < 1000000) {
    return (
      (num / 1000)
        .toFixed(1)
        .replace(/\.0$/, '')
        .replace(/\B(?=(\d{3})+(?!\d))/g, '.') + 'k'
    );
  } else if (num >= 1000000) {
    return (
      (num / 1000000)
        .toFixed(1)
        .replace(/\.0$/, '')
        .replace(/\B(?=(\d{3})+(?!\d))/g, '.') + 'M'
    );
  }
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

function formatVND(amount: number | string) {
  return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' USD';
}
function formatPriceVND(amount: number | string): string {
  if (amount) {
    const price = typeof amount === 'string' ? parseFloat(amount) : amount;
    return price.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' VND';
  }
}

function inputNumberFormatter(value: number) {
  return `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}
function convertCurrency(amount: number | string) {
  const lang = localStorage.getItem('lang') || 'vi';
  let value = typeof amount === 'string' ? parseFloat(amount) : amount;
  const exchangeRates = {
    vi: 1,
    en: 0.00004,
    cn: 0.00029,
  };

  let convertedAmount = value * (exchangeRates[lang] || 1);

  let formattedAmount = convertedAmount.toLocaleString(
    lang === 'vi' ? 'vi-VN' : lang === 'en' ? 'en-US' : 'zh-CN'
  );

  let currencySymbol = lang === 'vi' ? 'VND' : lang === 'en' ? 'USD' : '人民币';

  return `${formattedAmount} ${currencySymbol}`;
}

export {
  formatNumber,
  formatVND,
  inputNumberFormatter,
  convertCurrency,
  formatPriceVND,
};
