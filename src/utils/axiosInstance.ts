import axios, { AxiosResponse } from 'axios';

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('interceptors error:', error);

    return Promise.reject(error);
  }
);

export const setAuthorizationHeader = (token: string) => {
  axiosInstance.defaults.headers['Authorization'] = `Bearer ${token}`;
};

export const getApi = async <T, P>(url: string, params?: T) => {
  try {
    const response: AxiosResponse<P> = await axiosInstance.get(url, { params });

    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const postApi = async <T, P>(url: string, data: T) => {
  try {
    const response: AxiosResponse<P> = await axiosInstance.post(url, data);

    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const deleteApi = async (url: string) => {
  try {
    const response = await axiosInstance.delete(url);

    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default axiosInstance;
