import { FC, ReactNode } from 'react';
import { Navigate } from 'react-router-dom';

interface PrivateRouteProps {
  children: ReactNode;
}

// Replace this with your actual authentication check logic
const isAuthenticated = (): boolean => !!localStorage.getItem('token');

const PrivateRoute: FC<PrivateRouteProps> = ({ children }) => {
  // If the user is not authenticated, redirect to the login page
  if (!isAuthenticated()) {
    return <Navigate to="/login" />;
  }

  // If the user is authenticated, render the children components
  return <>{children}</>;
};

export default PrivateRoute;
