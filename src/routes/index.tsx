import { CaretUpOutlined } from '@ant-design/icons';
import { FloatButton } from 'antd';
import { lazy } from 'react';
import {
  createBrowserRouter,
  Navigate,
  RouterProvider,
} from 'react-router-dom';
import PrivateRoute from './PrivateRoute.route';
import { isAuthenticated } from '@/utils/auth';
import MemberProfileView from '@/views/profile/MemberProfileView';
import { ROUTES } from '@/enums/Routes';

const Dashboard = lazy(() => import('@/views/dashboard'));
const Home = lazy(() => import('@/views/home'));
const SearchByMenu = lazy(() => import('@/views/search-menu'));
// const Products = lazy(() => import('@/views/product'));
const CartItems = lazy(() => import('@/views/cart-items'));
const OrderItems = lazy(() => import('@/views/order-items'));
const ProductDetails = lazy(() => import('@/views/product/details'));
const NotFound = lazy(() => import('@/views/not-found'));
const Example = lazy(() => import('@/views/example'));
const Login = lazy(() => import('@/views/auth/LoginView'));
const Register = lazy(() => import('@/views/auth/RegisterView'));
const Voucher = lazy(() => import('@/views/voucher'));
const AboutUs = lazy(() => import('@/views/about-us'));
const PrivacyPolicy = lazy(() => import('@/views/privacy-policy'));
const OrderGuide = lazy(() => import('@/views/order-guide'));
const ReturnPolicy = lazy(() => import('@/views/return-policy'));
const ShippingMethod = lazy(() => import('@/views/shipping-method'));
const Contact = lazy(() => import('@/views/contact'));
const PaymentPolicy = lazy(() => import('@/views/payment-policy'));
const InspectionPolicy = lazy(() => import('@/views/inspection-policy'));
const WarrantyPolicy = lazy(() => import('@/views/warranty-policy'));
const TradingConditions = lazy(() => import('@/views/trading-conditions'));
const HappyShop = lazy(() => import('@/views/happy-shop'));
const ComplaintsPolicy = lazy(() => import('@/views/complaints-policy'));
const ForgotPassword = lazy(() => import('@/views/auth/ForgotPasswordView'));
const Otp = lazy(() => import('@/views/auth/OtpView'));
const UpdatePassword = lazy(() => import('@/views/auth/UpdatePassword'));

const PaymentSuccess = lazy(() => import('@/views/payment-success'));

const router = createBrowserRouter(
  [
    {
      path: '/',
      element: <Dashboard />,
      // eslint-disable-next-line no-sparse-arrays
      children: [
        { index: true, element: <Home /> },
        ,
        {
          path: 'login',
          element: <Login />,
        },
        {
          path: 'register',
          element: <Register />,
        },
        {
          path: 'profile/:tab',
          element: <MemberProfileView />,
        },
        {
          path: 'products/:categoryId',
          element: <SearchByMenu />,
        },
        {
          path: 'products',
          children: [
            // { index: true, element: <SearchByMenu /> }, // Protect Product routes
            { path: 'details/:id', element: <ProductDetails /> },
            { path: 'cart', element: <CartItems /> },
            { path: 'order', element: <OrderItems /> },
          ],
        },
        {
          path: 'example',
          element: (
            <PrivateRoute>
              <Example />
            </PrivateRoute>
          ), // Protected Example route
        },
        {
          path: ROUTES.VOUCHER,
          element: <Voucher />,
        },
        {
          path: 'about-us',
          element: <AboutUs />,
        },
        {
          path: 'privacy-policy',
          element: <PrivacyPolicy />,
        },
        {
          path: 'order-guide',
          element: <OrderGuide />,
        },
        {
          path: 'return-policy',
          element: <ReturnPolicy />,
        },
        {
          path: 'shipping-method',
          element: <ShippingMethod />,
        },
        {
          path: 'contact',
          element: <Contact />,
        },
        {
          path: 'payment-success',
          element: <PaymentSuccess />,
        },
        {
          path: 'payment-policy',
          element: <PaymentPolicy />,
        },
        {
          path: 'inspection-policy',
          element: <InspectionPolicy />,
        },
        {
          path: 'warranty-policy',
          element: <WarrantyPolicy />,
        },
        {
          path: 'trading-conditions',
          element: <TradingConditions />,
        },
        {
          path: 'happy-shop',
          element: <HappyShop />,
        },
        {
          path: 'complaints-policy',
          element: <ComplaintsPolicy />,
        },
        {
          path: 'forgot-password',
          element: <ForgotPassword />,
        },
        {
          path: 'otp',
          element: <Otp />,
        },
        {
          path: 'update-password',
          element: <UpdatePassword />,
        },
        { path: '*', element: <NotFound /> },
      ],
    },
  ],
  {
    future: {
      v7_relativeSplatPath: true, // Enables relative paths in nested routes
      v7_fetcherPersist: true, // Retains fetcher state during navigation
      v7_normalizeFormMethod: true, // Normalizes form methods (e.g., POST or GET)
      v7_partialHydration: true, // Supports partial hydration for server-side rendering
      v7_skipActionErrorRevalidation: true, // Prevents revalidation when action errors occur
    },
  }
);

const Routes = () => (
  <>
    <RouterProvider future={{ v7_startTransition: true }} router={router} />
    <FloatButton.BackTop icon={<CaretUpOutlined />} />
  </>
);

export default Routes;
