import { NSOrder } from '@/enums/NSOrder';
import { NSPayment } from '@/enums/NSPayment';
import { PrimaryBaseEntity } from './@common';

export class CreateOrderReq {
  listOrderItem: CreateOrderItemReq[];
  paymentMethod: NSOrder.EPaymentMethod;
  deliveryAddress: string;
  totalAmount: number;
  returnUrl?: string;
}

export class CreateOrderItemReq {
  productId: string;
  qty: number;
}

export class VnpayCreatePaymentReq {
  amount: number;
  purpose: NSPayment.EPaymentPurpose;
  bankCode?: string;
  returnUrl?: string;
  orderId?: string;
}

export class OrderDetailReq {
  id: string;
}

export interface OrderDetailDto {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  memberId: string;
  totalAmount: string;
  paymentMethod: string;
  deliveryAddress: string;
  status: any;
  member: Member;
  items: Item[];
}

export interface Member {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  username: string;
  phone: string;
  email: string;
  password: string;
  status: string;
  avatar: any;
  firstName: string;
  lastName: string;
  birthOfDate: string;
  personalId: any;
  frontPersonalIdCardUrl: any;
  backPersonalIdCardUrl: any;
  walletAddress: string;
  balance: number;
  exactBalance: string;
}

export interface Item {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  orderId: string;
  productId: string;
  qty: number;
  price: string;
  product: Product;
}

export interface Product {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  categoryId: string;
  name: string;
  nameVi: string;
  nameCn: string;
  buyNowPrice: string;
  qty: number;
  cover: string;
  shortDesc: any;
  desc: string;
  color: any;
  size: any;
  summary: any;
}

export interface MyOrderReq {
  pageIndex: number;
  pageSize: number;
  status?: NSOrder.EStatus;
}

export interface IMyOrderItem {
  id: string;
  qty: number;
  price: number;
  productId: string;
  productName: string;
  productNameVi: string;
  productNameCn: string;
  productImage: string;
}
export interface IMyOrder {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  memberId: string;
  paymentMethod: string;
  totalAmount: string;
  deliveryAddress: any;
  status: string;
  items: IMyOrderItem[];
}
