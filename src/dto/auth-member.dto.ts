import dayjs from 'dayjs';
import { PageRequest, PrimaryBaseEntity } from './@common';

export class MemberD<PERSON> extends PrimaryBaseEntity {
  username: string;
  phone: string;
  password: string;
  email: string;
  avatar: string;
  status: string;
  firstName: string;
  lastName: string;
  birthOfDate: string | null;
  personalId: string | null;
  frontPersonalIdCardUrl: string | null;
  backPersonalIdCardUrl: string | null;
  walletAddress: string;
  balance: number;
  exactBalance: string;
  address: string;
}

export class MyAddressDto extends PrimaryBaseEntity {
  isDefault: boolean;
  provinceCode: number;
  districtCode: number;
  wardCode: number;
  address: string;
}

export class RegisterMemberReq {
  phone: string;
  password: string;
  email: string;
  avatar: string;
  firstName: string;
  lastName: string;
  birthOfDate: dayjs.Dayjs;
}

export class LoginMemberReq {
  username: string;
  password: string;
}

export class AuthMemberDto {
  accessToken: string;
  refreshToken: string;
  member: MemberDto;
  expiresIn: number;
}

export class ListTicketsReq extends PageRequest {
  fromDate?: Date;
  toDate?: Date;
}

export class TicketsDto extends PrimaryBaseEntity {
  ticketNumber: number;
}

export class ListNotificationsReq extends PageRequest {}

export class MyAddressReq extends PageRequest {}

export class ListNotificationsDto extends PrimaryBaseEntity {
  id: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  memberId: string;
  title: string;
  titleVi: string;
  titleCn: string;
  body: string;
  bodyVi: string;
  bodyCn: string;
  isRead: boolean;
  data: any;
}

export class ChangePasswordReq {
  oldPassword: string;
  newPassword: string;
}

export interface UpdateProfileReq {
  email?: string;
  firstName?: string;
  phone?: string;
  address?: string;
  avatar?: string;
}
