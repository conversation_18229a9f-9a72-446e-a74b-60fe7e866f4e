import { PageRequest, PrimaryBaseEntity } from './@common';
import { MemberDto } from './auth-member.dto';

export class ListProductsReq extends PageRequest {
  categoryId?: string;
  sectionId?: string;
  keyword?: string;
  sectionCode?: string;
}
export class ListLatestRevealReq extends PageRequest {}
export class ListComingSoonReq extends PageRequest {}

export class ProductsDto extends PrimaryBaseEntity {
  id: string;
  categoryId: string;
  desc: string;
  qty: number;
  color: string;
  size: string;
  summary: string;
  cover: string;
  name: string;
  nameVi?: string;
  nameCn?: string;
  buyNowPrice: number;
  shortDesc: string;
}

export class ProductDetailReq {
  id: string;
}

export class ProductDetailDto extends PrimaryBaseEntity {
  id: string;
  name: string;
  nameVi?: string;
  nameCn?: string;
  category: Category;
  buyNowPrice: number;
  qty: string;
  cover: string;
  desc: string;
  color: string;
  size: string;
  summary: string;
  remain: string;
  ticketSessions?: TicketSession[];
  sections: any[];
  shortDesc: string;
  images: {
    id: string;
    image: string;
    position: number;
  }[];
}

export class Category {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: string;
  updatedBy: string;
  version: number;
  parentId: string;
  desc: string;
  image: string;
  level: number;
  name: string;
}

export class TicketSession extends PrimaryBaseEntity {
  productId: string;
  name: string;
  ticketPrice: number;
  totalTickets: number;
  soldTickets: number;
  status: string;
  winnerId?: string;
  winningTicketNumber?: string;
}
export class LatestRevealRes extends TicketSession {
  product?: ProductsDto;
  winner?: MemberDto;
}
export class ComingSoonRes extends TicketSession {
  product?: ProductsDto;
}

export class SearchProductByNameReq {
  name: string;
}

export class ListWinners extends TicketSession {
  product?: ProductsDto;
}

export class ListBuyedTicketReq extends PageRequest {
  productId: string;
}

export interface ListUsersBuyedTicket {
  data: Daum[]
  total: number
}

export interface Daum {
  sessionId: string
  productId: string
  member: Member
  qty: string
}

export interface Member {
  id: string
  avatar: any
  firstName: string
  lastName: string
  username: string
  phone: string
  email: string
}