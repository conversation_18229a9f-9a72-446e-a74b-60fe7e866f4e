import { FC, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import Container from '@/components/home/<USER>';
import { Text } from '@/components';
import { Grid } from 'antd';
import { useTranslation } from 'react-i18next';

const contentVi = `<h1 style="font-size: 24px; text-align: center;">CHÍNH SÁCH BẢO MẬT THÔNG TIN</h1><div style="padding: 5px;"><b>1. <PERSON><PERSON><PERSON> đích thu thập thông tin cá nhân:</b></div><div>Shop Lucky Viet Nam tiến hành thu thập thông tin khi được khách hàng cho phép nhằm các mục đích sau</div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>Hỗ trợ khách hàng: mua hàng, thanh toán, giao hàng.</li><li><PERSON><PERSON> cấp thông tin sản phẩm, các dịch vụ và hỗ trợ theo yêu cầu của khách hàng.</li><li><PERSON><PERSON><PERSON> thông báo các chương trình, sản phẩm mới nhất của chúng tôi.</li><li>Giải quyết vấn đề phát sinh khi mua hàng.</li></ul><div style="padding: 5px;"><b>2. Phạm vi thu thập thông tin:</b></div><div>Shop Lucky Viet Nam chỉ tiến hành thu thập thông tin khi được khách hàng cho phép trong các trường hợp khách mua hàng trực tiếp hoặc đặt hàng trực tuyến. Các thông tin thu thập gồm: Họ tên ; Địa chỉ email ; Số điện thoại ; Địa chỉ giao hàng.</div><div style="padding: 5px;"><b>3. Thời gian lưu trữ thông tin</b></div><div>Dữ liệu cá nhân của khách hàng sẽ được lưu trữ cho đến khi khách hàng yêu cầu hủy bỏ hoặc Shop Lucky Viet Nam chủ động hủy bỏ. Còn lại trong mọi trường hợp thông tin cá nhân khách hàng sẽ được bảo mật và không sử dụng cho các mục đích khác ngoài mục 1 ở trên.</div><div style="padding: 5px;"><b>4. Những người hoặc tổ chức có thể được tiếp cận với thông tin đó:</b></div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>Đối với các bên vận chuyển, sẽ cung cấp các thông tin để phục vụ cho việc giao nhận hàng hóa như Tên, địa chỉ và số điện thoại.</li><li>Đối với nhân viên công ty sẽ có các bộ phận chuyên trách để phục vụ việc chăm sóc khách hàng trong quá trình sử dụng sản phẩm.</li><li>Các chương trình có tính liên kết, đồng thực hiện, thuê ngoài cho các mục đích được nêu tại Mục 1 và luôn áp dụng các yêu cầu bảo mật thông tin cá nhân.</li><li>Yêu cầu pháp lý: Chúng tôi có thể tiết lộ các thông tin cá nhân nếu điều đó do luật pháp yêu cầu và việc tiết lộ như vậy là cần thiết một cách hợp lý để tuân thủ các quy trình pháp lý. Chuyển giao kinh doanh (nếu có): trong trường hợp sáp nhập, hợp nhất toàn bộ hoặc một phần với công ty khác, người mua sẽ có quyền truy cập thông tin được chúng tôi lưu trữ, duy trì trong đó bao gồm cả thông tin cá nhân.</li></ul><div style="padding: 5px;">*Cơ chế tiếp nhận và giải quyết khiếu nại của người tiêu dùng liên quan đến việc thông tin cá nhân bị sử dụng sai mục đích hoặc phạm vi đã thông báo: Tại Shop Lucky Viet Nam, việc bảo vệ thông tin cá nhân của khách hàng là rất quan trọng, khách hàng được đảm bảo rằng thông tin cung cấp cho chúng tôi sẽ được mật. Shop Lucky Viet Nam cam kết không chia sẻ, bán hoặc cho thuê thông tin cá nhân của bạn cho bất kỳ người nào khác. Shop Lucky Viet Nam cam kết chỉ sử dụng các thông tin của bạn vào các trường hợp sau:</div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>Nâng cao chất lượng dịch vụ dành cho khách hàng</li><li>Giải quyết các tranh chấp, khiếu nại trong vòng 3 ngày sau khi nhận được thông tin.</li><li>Khi cơ quan pháp luật có yêu cầu.</li></ul><div style="padding: 5px;">Shop Lucky Viet Nam hiểu rằng quyền lợi của khách hàng trong việc bảo vệ thông tin cá nhân cũng chính là trách nhiệm của chúng tôi nên trong bất kỳ trường hợp có thắc mắc, góp ý nào liên quan đến chính sách bảo mật của Shop Lucky Viet Nam, và liên quan đến việc thông tin cá nhân bị sử dụng sai mục đích hoặc phạm vi đã thông báo vui lòng liên hệ qua số hotlinea 0909 689 026 để xử lý và làm việc trực tiếp với khách hàng.<br>Shop Lucky Viet Nam xin cảm ơn quý khách hàng đã tin tưởng và hợp tác!</div>`;
const contentEn = `<h1 style="font-size: 24px; text-align: center;">PRIVACY POLICY</h1>
<p  dir="ltr"><span ><span "><strong>1. Purpose of collecting personal information:</strong></span></span></p>
<p  dir="ltr"><span ><span ">Shop Lucky Vietnam collects personal information only with customer permission for the following purposes:</span></span></p>
<p  dir="ltr"><span ><span ">Customer support: purchasing, payment, delivery.</span></span></p>
<p  dir="ltr"><span ><span ">Providing product information, services, and support as requested by customers.</span></span></p>
<p  dir="ltr"><span ><span ">Sending notifications about our latest programs and products.</span></span></p>
<p  dir="ltr"><span ><span ">Resolving issues that arise during the purchasing process.</span></span></p>
<p  dir="ltr"><span ><span "><strong>2. Scope of information collection:</strong></span></span></p>
<p  dir="ltr"><span ><span ">Shop Lucky Vietnam only collects information with customer consent in cases of direct purchase or online ordering. Collected information includes: Full name; Email address; Phone number; Delivery address.</span></span></p>
<p  dir="ltr"><span ><span "><strong>3. Duration of information storage:</strong></span></span></p>
<p  dir="ltr"><span ><span ">Customer personal data is stored until the customer requests deletion or Shop Lucky Vietnam proactively deletes it. In all other cases, personal data will be kept confidential and not used for any purposes other than those mentioned in section 1.</span></span></p>
<p  dir="ltr"><span ><span "><strong>4. Individuals or organizations that may access this information:</strong></span></span></p>
<p  dir="ltr"><span ><span ">Shipping partners will receive necessary details such as name, address, and phone number for delivery purposes.</span></span></p>
<p  dir="ltr"><span ><span ">Company employees from specific departments may access data to support customer care during product use.</span></span></p>
<p  dir="ltr"><span ><span ">Partner programs, co-implementation, or outsourcing for the purposes stated in section 1 will adhere to strict privacy policies.</span></span></p>
<p  dir="ltr"><span ><span ">Legal requirement: We may disclose personal information if required by law and such disclosure is reasonably necessary to comply with legal processes. Business transfers (if applicable): In the event of a merger, acquisition, or partial consolidation with another company, the buyer may access our stored information, including personal data.</span></span></p>
<p  dir="ltr"><span ><span ">*Mechanism for receiving and resolving customer complaints related to misuse of personal information: At Shop Lucky Vietnam, protecting customer privacy is of utmost importance. Customers are assured that any information provided will be kept confidential. Shop Lucky Vietnam commits not to share, sell, or lease your personal information to any third party. We only use your information in the following cases:</span></span></p>
<p  dir="ltr"><span ><span ">Improving service quality for customers.</span></span></p>
<p  dir="ltr"><span ><span ">Resolving disputes or complaints within 3 days after receiving notice.</span></span></p>
<p  dir="ltr"><span ><span ">When required by legal authorities.</span></span></p>
<p  dir="ltr"><span ><span ">Shop Lucky Vietnam understands that protecting customer personal information is also our responsibility. If you have any concerns, suggestions, or complaints related to this privacy policy or misuse of personal data, please contact our hotline at 0909 689 026 for resolution and direct assistance.</span></span></p>
<p  dir="ltr"><span ><span ">Thank you for trusting and cooperating with Shop Lucky Vietnam!</span></span></p>`;
const contentCn = `<h1 style="font-size: 24px; text-align: center;">信息隐私政策</h1><p>
    <meta charset="utf-8">
</p>
<p  dir="ltr"><span ><span ><strong>1. 收集个人信息的目的：</strong></span></span></p>
<p  dir="ltr"><span ><span >Shop Lucky 越南仅在客户授权的情况下收集个人信息，目的如下：</span></span></p>
<p  dir="ltr"><span ><span >客户支持：购物、付款、交付。</span></span></p>
<p  dir="ltr"><span ><span >根据客户要求提供产品信息、服务和支持。</span></span></p>
<p  dir="ltr"><span ><span >发送我们的最新活动和产品通知。</span></span></p>
<p  dir="ltr"><span ><span >解决购物过程中出现的问题。</span></span></p>
<p  dir="ltr"><span ><span ><strong>2. 信息收集范围：</strong></span></span></p>
<p  dir="ltr"><span ><span >Shop Lucky 越南仅在客户允许的情况下，在客户直接购买或在线订购的情况下收集信息。收集的信息包括：姓名、电子邮件地址、电话号码、收货地址。</span></span></p>
<p  dir="ltr"><span ><span ><strong>3. 信息存储时间：</strong></span></span></p>
<p  dir="ltr"><span ><span >客户的个人信息将被保留，直到客户请求删除或 Shop Lucky 越南主动删除。除此之外，在所有情况下，客户的个人信息都将被保密，不会用于第 1 条中所列目的以外的任何其他目的。</span></span></p>
<p  dir="ltr"><span ><span ><strong>4. 可接触该信息的个人或组织：</strong></span></span></p>
<p  dir="ltr"><span ><span >物流公司将获取客户的姓名、地址和电话等信息，以便完成货物配送。</span></span></p>
<p  dir="ltr"><span ><span >公司员工中有专门负责客户服务的部门可访问这些信息。</span></span></p>
<p  dir="ltr"><span ><span >为实现第 1 条中所述目的的合作项目、联合执行或外包项目将始终遵守严格的隐私政策。</span></span></p>
<p  dir="ltr"><span ><span >法律要求：在法律要求的情况下，我们可能会披露个人信息，此类披露在合理范围内是为了遵守法律程序。业务转让（如有）：在并购或部分合并的情况下，买方将有权访问我们存储的信息，包括个人信息。</span></span></p>
<p  dir="ltr"><span ><span >*接收和解决消费者关于个人信息被误用的投诉机制：在 Shop Lucky 越南，客户的个人信息安全非常重要，客户可放心提供的信息将被保密。Shop Lucky 越南承诺不与任何第三方分享、出售或出租您的个人信息。我们仅在以下情况下使用您的信息：</span></span></p>
<p  dir="ltr"><span ><span >提升客户服务质量。</span></span></p>
<p  dir="ltr"><span ><span >在收到投诉后 3 天内解决争议。</span></span></p>
<p  dir="ltr"><span ><span >当法律机关要求时。</span></span></p>
<p  dir="ltr"><span ><span >Shop Lucky 越南理解，保护客户个人信息是我们的责任。如果您对 Shop Lucky 越南的隐私政策有任何疑问、意见或投诉，或认为您的信息被用于未通知的目的，请拨打热线 0909 689 026 联系我们以获得直接帮助。</span></span></p>
<p  dir="ltr"><span style="background-color:transparent;color:#000000;font-family:'MS Mincho';font-size:12pt;"><span >感谢您对</span></span><span style="background-color:transparent;color:#000000;font-family:'Times New Roman',serif;font-size:12pt;"><span > Shop Lucky&nbsp;</span></span><span style="background-color:transparent;color:#000000;font-family:'MS Mincho';font-size:12pt;"><span >越南的信任与合作！</span></span></p>`;

const PrivacyPolicy: FC = () => {
  const { md } = Grid.useBreakpoint();
  const [content, setContent] = useState(contentVi);
  const { i18n } = useTranslation();

  useEffect(() => {
    if (i18n.language === 'vi') {
      setContent(contentVi);
    } else if (i18n.language === 'en') {
      setContent(contentEn);
    } else if (i18n.language === 'cn') {
      setContent(contentCn);
    }
  }, [i18n.language]);

  return (
    <Container
      style={{
        marginTop: md ? 120 : 40,
        position: md ? 'unset' : 'relative',
        backgroundColor: '#FFFFFF',
        paddingBottom: 20,
      }}
    >
      <div dangerouslySetInnerHTML={{ __html: content }}></div>
    </Container>
  );
};

export default PrivacyPolicy;
