import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  DatePicker,
  Form,
  Grid,
  Input,
  Layout,
  Row,
  Typography,
} from 'antd';
import Link from 'antd/es/typography/Link';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import bgImage from '/images/background-login.jpg';
import shopImage from '/images/shop-text-01.png';
import { useRegister } from './hooks/useRegister';
import { Language } from '@/components';

const RegisterView: FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { Text, Title } = Typography;
  const { md } = Grid.useBreakpoint();

  const { form, isLoading, onFinish } = useRegister();

  return (
    <Layout
      style={{
        height: '100vh',
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
      }}
    >
      <Row style={{ padding: 12 }} justify={'end'}>
        <Language />
      </Row>
      <Row
        gutter={24}
        justify="center"
        // align="middle"
        style={{
          width: '100%',
          height: '100%',
          margin: 0,
          alignItems: md ? 'center' : 'normal',
        }}
      >
        <Col
          xl={16}
          sm={12}
          style={{ display: 'flex', justifyContent: 'center' }}
        >
          <img
            src={shopImage}
            alt="cover"
            loading="lazy"
            style={{
              width: 600,
              objectFit: 'contain',
              overflow: 'hidden',
            }}
          />
        </Col>
        <Col xl={8} sm={12}>
          <Card
            style={{
              width: md ? '450px' : '100%',
              minWidth: '340px',
              // height: '500px',
              display: 'flex',
              justifyContent: 'center',
              flexDirection: 'column',
              borderRadius: '8px',
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
              backgroundColor: 'white',
            }}
          >
            <Form
              name="register"
              form={form}
              onFinish={onFinish}
              style={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
              }}
            >
              {/* Title */}
              <Title
                level={2}
                style={{ textAlign: 'center', marginBottom: '20px' }}
              >
                {t('auth.registerTitle')}
              </Title>

              {/* Username Input */}
              <Form.Item
                name="phone"
                style={{ width: '100%', marginBottom: '16px' }}
                rules={[
                  { required: true, message: t('auth.usernameRequired') },
                ]}
              >
                <Input
                  placeholder={t('auth.phone')}
                  style={{ width: '100%', padding: '10px' }}
                />
              </Form.Item>

              {/* Password Input */}
              <Form.Item
                name="password"
                style={{ width: '100%', marginBottom: '16px' }}
                rules={[
                  { required: true, message: t('auth.passwordRequired') },
                ]}
              >
                <Input.Password
                  placeholder={t('auth.password')}
                  style={{ width: '100%', padding: '10px' }}
                />
              </Form.Item>

              {/* Password Input */}
              <Form.Item
                name="firstName"
                style={{ width: '100%', marginBottom: '16px' }}
              >
                <Input
                  placeholder={t('auth.firstName')}
                  style={{ width: '100%', padding: '10px' }}
                />
              </Form.Item>

              {/* Email Input */}
              <Form.Item
                name="email"
                style={{ width: '100%', marginBottom: '16px' }}
                rules={[{ required: true, message: t('auth.email_required') }]}
              >
                <Input
                  placeholder={t('auth.email')}
                  style={{ width: '100%', padding: '10px' }}
                />
              </Form.Item>

              {/* <Form.Item
                name="lastName"
                style={{ width: '100%', marginBottom: '16px' }}
              >
                <Input
                  placeholder={t('auth.lastName')}
                  style={{ width: '100%', padding: '10px' }}
                />
              </Form.Item> */}

              {/* birthOfDate Input */}
              <Form.Item
                name="birthOfDate"
                style={{ width: '100%', marginBottom: '16px' }}
                rules={[
                  { required: true, message: t('auth.birthday_required') },
                ]}
              >
                <DatePicker
                  placeholder={t('auth.birthday')}
                  style={{ width: '100%', padding: '10px' }}
                />
              </Form.Item>

              {/* Register Button */}
              <Form.Item style={{ width: '100%', marginBottom: '16px' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  style={{ width: '100%', backgroundColor: '#F37421' }}
                >
                  {t('auth.register')}
                </Button>
              </Form.Item>

              {/* Login Link */}
              <Form.Item>
                <Text>
                  {t('auth.hasAccount')}{' '}
                  <Link href="/login" style={{ color: '#F37421' }}>
                    {t('auth.login')}
                  </Link>
                </Text>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </Layout>
  );
};

export default RegisterView;
