import {
  But<PERSON>,
  Card,
  Col,
  Form,
  Grid,
  Input,
  Layout,
  Row,
  Typography,
} from 'antd';
import Link from 'antd/es/typography/Link';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import bgImage from '/images/background-login.jpg';
import shopImage from '/images/shop-text-01.png';
import { useLogin } from './hooks/useLogin';
import { Language } from '@/components';
import { Checkbox } from 'antd';
import type { CheckboxProps } from 'antd';

const LoginView: FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { Text, Title } = Typography;
  const { md } = Grid.useBreakpoint();

  const { form, isLoading, onFinish } = useLogin();
  const [isPolicy, setIsPolicy] = useState<boolean>(true);

  const onChange: CheckboxProps['onChange'] = (e) => {
    setIsPolicy(e.target.checked);
  };

  return (
    <Layout
      style={{
        height: '100vh',
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
      }}
    >
      <Row style={{ padding: 12 }} justify={'end'}>
        <Language />
      </Row>

      <Row
        gutter={24}
        justify="center"
        style={{
          width: '100%',
          height: '100%',
          margin: 0,
          alignItems: md ? 'center' : 'normal',
        }}
      >
        <Col
          xl={16}
          sm={12}
          style={{ display: 'flex', justifyContent: 'center' }}
        >
          <img
            src={shopImage}
            alt="cover"
            loading="lazy"
            style={{
              width: 600,
              objectFit: 'contain',
              overflow: 'hidden',
            }}
          />
        </Col>
        <Col
          xl={8}
          sm={12}
          style={{ display: 'flex', justifyContent: 'center' }}
        >
          <Card
            style={{
              width: md ? '450px' : '100%',
              height: '400px',
              minWidth: '340px',
              display: 'flex',
              justifyContent: 'center',
              flexDirection: 'column',
              borderRadius: '8px',
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
              backgroundColor: 'white',
            }}
          >
            {/* Login Form */}
            <Form
              name="login"
              form={form}
              onFinish={onFinish}
              style={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
              }}
            >
              {/* Title */}
              <Title
                level={2}
                style={{
                  textAlign: 'center',
                  marginBottom: '20px',
                  color: '#F37421',
                  width: '100%',
                }}
              >
                {t('auth.welcome')}
              </Title>

              {/* Username Input */}
              <Form.Item
                name="username"
                style={{ width: '100%', marginBottom: '16px' }}
                rules={[
                  { required: true, message: t('auth.usernameRequired') },
                ]}
              >
                <Input
                  placeholder={t('auth.username')}
                  style={{ width: '100%', padding: '10px' }}
                />
              </Form.Item>

              {/* Password Input */}
              <Form.Item
                name="password"
                style={{ width: '100%', marginBottom: '16px' }}
                rules={[
                  { required: true, message: t('auth.passwordRequired') },
                ]}
              >
                <Input.Password
                  placeholder={t('auth.password')}
                  style={{ width: '100%', padding: '10px' }}
                />
              </Form.Item>
              {/* <Link href="/privacy-policy" style={{ marginBottom: '10px' }}>
                <Checkbox onChange={onChange} />
                <Text style={{ marginLeft: 8 }}>{t('auth.privacyPolicy')}</Text>
              </Link> */}

              {/* Login Button */}
              <Form.Item style={{ width: '100%', marginBottom: '16px' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  style={{ width: '100%', backgroundColor: '#F37421' }}
                  disabled={!isPolicy}
                >
                  {t('auth.login')}
                </Button>
              </Form.Item>

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  width: '100%',
                }}
              >
                <Link href="/forgot-password">
                  <Text>{t('auth.forgot')}</Text>
                </Link>
                <Link href="/register">
                  <Text>{t('auth.register')}</Text>
                </Link>
              </div>

              {/* Register Link */}
              {/* <Form.Item>
                <Text>
                  {t('auth.noAccount')}{' '}
                  <Link href="/register" style={{ color: '#1890ff' }}>
                    {t('auth.register')}
                  </Link>
                </Text>
              </Form.Item> */}
            </Form>
          </Card>
        </Col>
      </Row>
    </Layout>
  );
};

export default LoginView;
