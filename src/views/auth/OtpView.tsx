import React, { useState } from 'react';
import { Typography, Input, Button, Row, Col, Layout, Grid } from 'antd';
import { useTranslation } from 'react-i18next';
import bgImage from '/images/background-login.jpg';
import shopImage from '/images/shop-text-01.png';
import Link from 'antd/es/typography/Link';
import { Language } from '@/components';
import { LeftOutlined } from '@ant-design/icons';
import { OTPProps } from 'antd/es/input/OTP';

const OtpView = () => {
  const { t } = useTranslation();
  const { Text, Title } = Typography;
  const { md } = Grid.useBreakpoint();
  const [value, setValue] = useState('');
  const onChange: OTPProps['onChange'] = (text) => {
    console.log('onChange:', text);
  };

  const onInput: OTPProps['onInput'] = (value) => {
    console.log('onInput:', value);
  };

  const sharedProps: OTPProps = {
    onChange,
    onInput,
  };
  const handleChange = (val) => {
    if (val.length <= 6) {
      setValue(val.toUpperCase());
    }
  };
  return (
    <Layout
      style={{
        height: '100vh',
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
      }}
    >
      <Row style={{ padding: 12 }} justify={'end'}>
        <Language />
      </Row>
      <Row
        gutter={24}
        justify="center"
        style={{
          width: '100%',
          height: '100%',
          margin: 0,
          alignItems: md ? 'center' : 'normal',
        }}
      >
        <Col
          xl={16}
          sm={12}
          style={{ display: 'flex', justifyContent: 'center' }}
        >
          <img
            src={shopImage}
            alt="cover"
            loading="lazy"
            style={{ width: 600, objectFit: 'contain', overflow: 'hidden' }}
          />
        </Col>
        <Col xl={8} sm={12}>
          <div
            style={{
              maxWidth: 400,
              margin: 'auto',
              padding: 24,
              background: 'linear-gradient(to right, #fff, #ffe8cc)',
              borderRadius: 12,
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            }}
          >
            <Link
              href="/login"
              style={{ marginBottom: '10px', textAlign: 'left' }}
            >
              <LeftOutlined />
              <Text>{t('auth.return')}</Text>
            </Link>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ marginBottom: 8 }}>
                {t('auth.verify_phone_number')}
              </Title>
              <Text>
                {t('auth.send_verification_code', { phone: '090*****26' })}
              </Text>

              <div style={{ marginTop: 24 }}>
                <Text strong>{t('auth.enter_otp_code')}</Text>
                <Row justify="center" gutter={8} style={{ marginTop: 12 }}>
                  <Input.OTP
                    style={{ height: '50px' }}
                    value={value}
                    onChange={handleChange}
                    {...sharedProps}
                    size="large"
                  />
                </Row>
              </div>
              <div style={{ marginTop: 16 }}>
                <Text>{t('auth.noOtp')}</Text>
                <Text type="warning" style={{ cursor: 'pointer' }}>
                  {t('auth.reSend')}
                </Text>
              </div>
              <Link href="/update-password">
                <Button
                  type="primary"
                  block
                  style={{ marginTop: 24, backgroundColor: '#F37421' }}
                >
                  {t('auth.confirm')}
                </Button>
              </Link>
            </div>
          </div>
        </Col>
      </Row>
    </Layout>
  );
};

export default OtpView;
