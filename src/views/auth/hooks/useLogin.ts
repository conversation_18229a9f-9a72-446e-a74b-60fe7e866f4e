import { LoginMemberReq, RegisterMemberReq } from '@/dto/auth-member.dto';
import { toastService } from '@/services/@common';
import authMembersService from '@/services/auth-members.service';
import { useAuthStore } from '@/stores/authStore';
import { Form } from 'antd';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

export const useLogin = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const { authenticate } = useAuthStore();

  const onFinish = async (values: LoginMemberReq) => {
    console.log('Received values of form: ', values);
    setIsLoading(true);
    try {
      const res = await authMembersService.login({ ...values });
      localStorage.setItem('token', res?.accessToken);
      authenticate();
      navigate('/');
      toastService.success('Login success');
    } catch (error) {
      toastService.handleError(error);
      console.log(`-------------------`);
      console.log({ error });
      console.log(`-------------------`);
    }
    setIsLoading(false);
  };

  return {
    form,
    isLoading,
    onFinish,
  };
};
