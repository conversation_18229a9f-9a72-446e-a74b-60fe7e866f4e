import { RegisterMemberReq } from '@/dto/auth-member.dto';
import { toastService } from '@/services/@common';
import authMembersService from '@/services/auth-members.service';
import { Form } from 'antd';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

export const useRegister = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  const onFinish = async (values: RegisterMemberReq) => {
    console.log('Received values of form: ', values);
    setIsLoading(true);
    try {
      await authMembersService.register({
        ...values,
        birthOfDate: values?.birthOfDate.toISOString() as any,
      });
      toastService.success('Register success');
      navigate('/login');
      form.resetFields();
    } catch (error) {
      toastService.handleError(error);
      console.log(`-------------------`);
      console.log({ error });
      console.log(`-------------------`);
    }
    setIsLoading(false);
  };

  return {
    form,
    isLoading,
    onFinish,
  };
};
