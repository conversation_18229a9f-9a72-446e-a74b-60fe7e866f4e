// import React from 'react';
import { Typography, Input, Button, Row, Col, Layout, Grid, Form } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import bgImage from '/images/background-login.jpg';
import shopImage from '/images/shop-text-01.png';
import { useTranslation } from 'react-i18next';
import Link from 'antd/es/typography/Link';
import { Language } from '@/components';

const ForgotPasswordView = () => {
    const { t } = useTranslation();
    const { Text, Title } = Typography;
    const { md } = Grid.useBreakpoint();
    return (
        <Layout style={{ height: '100vh', backgroundImage: `url(${bgImage})`, backgroundSize: 'cover', }}>
            <Row style={{ padding: 12 }} justify={'end'}>
                <Language />
            </Row>
            <Row
                gutter={24}
                justify="center"
                style={{ width: '100%', height: '100%', margin: 0, alignItems: md ? 'center' : 'normal' }}
            >
                <Col xl={16} sm={12} style={{ display: 'flex', justifyContent: 'center' }}>
                    <img
                        src={shopImage}
                        alt="cover"
                        loading="lazy"
                        style={{ width: 600, objectFit: 'contain', overflow: 'hidden', }}
                    />
                </Col>
                <Col xl={8} sm={12} >
                    <div
                    style={{
                        maxWidth: 400,
                        margin: 'auto',
                        padding: '24px 12px',
                        background: 'linear-gradient(to right, #fff, #ffe8cc)',
                        borderRadius: 12,
                        textAlign: 'left',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    }}
                    >
                    <Link href="/login" style={{ marginBottom: '10px' }}>
                        <LeftOutlined />
                        <Text>
                        {t('auth.forgot')}
                        </Text>
                    </Link>

                    <Title level={5}>{t('auth.enterUserName')}</Title>
                    <Row style={{ marginTop: 12, width: '100%' }}>
                        <Form.Item
                            name="phone"
                            style={{ width: '100%', marginBottom: '16px' }}
                            rules={[
                            { required: true, message: t('auth.phoneRequired') },
                            ]}
                        >
                            <Input
                            placeholder={t('auth.phone')}
                            style={{ width: '100%', padding: '10px' }}
                            />
                        </Form.Item>
                    </Row>


                    </div>
                </Col>
            </Row>
        </Layout>
    );
};

export default ForgotPasswordView;
