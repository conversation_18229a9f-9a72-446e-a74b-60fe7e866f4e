import { FC } from 'react';
import { Link } from 'react-router-dom';
import Container from '@/components/home/<USER>';
import { Text } from '@/components';
import { Grid } from 'antd';
import { useTranslation } from 'react-i18next';

const ShippingMethod: FC = () => {
  const { t } = useTranslation();
  const ulStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
  };
  const contentStyle: React.CSSProperties = {
    padding: '5px',
  };
  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    textAlign: 'center',
  };
  const { md } = Grid.useBreakpoint();
  return (
    <Container
      style={{
        marginTop: md ? 120 : 40,
        position: md ? 'unset' : 'relative',
        backgroundColor: '#FFFFFF',
      }}
    >
      <h1 style={{ ...titleStyle }}>{t('page.shipping.title')}</h1>
      <p className="mb-4">{t('page.shipping.intro')}</p>

      <h4 className="font-semibold mt-6 mb-2">
        {t('page.shipping.area.title')}
      </h4>
      <p>{t('page.shipping.area.content')}</p>

      <h4 className="font-semibold mt-6 mb-2">
        {t('page.shipping.methods.title')}
      </h4>
      <ul className="list-disc pl-5 space-y-1">
        <li>{t('page.shipping.methods.grab')}</li>
        <li>{t('page.shipping.methods.viettel')}</li>
      </ul>

      <h4 className="font-semibold mt-6 mb-2">
        {t('page.shipping.time.title')}
      </h4>
      <p>{t('page.shipping.time.content')}</p>

      <h4 className="font-semibold mt-6 mb-2">
        {t('page.shipping.fees.title')}
      </h4>
      <ul className="list-disc pl-5 space-y-1">
        <li>{t('page.shipping.fees.city')}</li>
        <li>{t('page.shipping.fees.outside')}</li>
      </ul>

      <h4 className="font-semibold mt-6 mb-2">
        {t('page.shipping.process.title')}
      </h4>
      <ul className="list-decimal pl-5 space-y-1">
        <li>{t('page.shipping.process.confirm')}</li>
        <li>{t('page.shipping.process.pack')}</li>
        <li>{t('page.shipping.process.deliver')}</li>
        <li>{t('page.shipping.process.receive')}</li>
      </ul>

      <h4 className="font-semibold mt-6 mb-2">
        {t('page.shipping.payment.title')}
      </h4>
      <p>{t('page.shipping.payment.cod')}</p>

      <h4 className="font-semibold mt-6 mb-2">
        {t('page.shipping.note.title')}
      </h4>
      <ul className="list-disc pl-5 space-y-1">
        <li>{t('page.shipping.note.content1')}</li>
        <li>{t('page.shipping.note.content2')}</li>
        <li>{t('page.shipping.note.contact')}</li>
      </ul>

      <p className="mt-6 font-semibold">{t('page.shipping.closing')}</p>
      {/* <Text>Shop Lucky Viet Nam cam kết mang đến cho quý khách hàng dịch vụ vận chuyển nhanh chóng và tiện lợi nhất. Dưới đây là những thông tin chi tiết về chính sách vận chuyển của chúng tôi:</Text>
      <Text style={contentStyle}><b>1. Phạm vi giao hàng</b></Text>
      <Text>Toàn Quốc: Chúng tôi giao hàng đến tất cả các tỉnh thành trên toàn quốc.</Text>
      <Text style={contentStyle}><b>2. Phương thức vận chuyển:</b></Text>
      <ul style={{...ulStyle}}>
        <li>Grab: Phù hợp cho đơn hàng có yêu cầu giao hàng nhanh trong thành phố Hồ Chí Minh</li>
        <li>Viettel post: Phù hợp cho đơn hàng ngoại thành có trọng lượng lớn hoặc kích thước cồng kềnh.</li>
      </ul>
      <Text style={contentStyle}><b>3. Thời gian giao hàng:</b></Text>
      <ul style={{...ulStyle}}>
        <li>Miễn phí vận chuyển: Chúng tôi luôn khuyến mãi miễn phí vận chuyển để quý khách có trải nghiệm mua hàng tốt nhất.</li>
      </ul>
      <Text style={contentStyle}><b>4. Phí vận chuyển:</b></Text>
      <ul style={{...ulStyle}}>
        <li>Nội thành: Thời gian giao 1-3 ngày làm việc</li>
        <li>Ngoại thành: Thời gian giao 3-5 ngày làm việc</li>
      </ul>
      <Text style={contentStyle}><b>5. Quy trình giao hàng:</b></Text>
      <ul style={{...ulStyle}}>
        <li>Xác nhận đơn hàng: Sau khi đặt hàng thành công, quý khách sẽ nhận được email hoặc tin nhắn xác nhận đơn hàng.</li>
        <li>Đóng gói hàng hóa: Chúng tôi sẽ tiến hành đóng gói sản phẩm cẩn thận và đảm bảo hàng hóa đến tay quý khách trong tình trạng nguyên vẹn.</li>
        <li>Giao hàng: Đơn vị vận chuyển sẽ giao hàng đến địa chỉ quý khách đã cung cấp.</li>
        <li>Nhận hàng: Khi nhận hàng, quý khách vui lòng kiểm tra kỹ sản phẩm trước khi thanh toán.</li>
      </ul>
      <Text style={contentStyle}><b>6. Thanh toán:</b></Text>
      <ul style={{...ulStyle}}>
        <li>Thanh toán khi nhận hàng (COD): Quý khách thanh toán trực tiếp cho nhân viên giao hàng.</li>
      </ul>
      <Text><b style={{ textDecoration: 'underline' }}>Lưu ý:</b></Text>
      <ul style={{...ulStyle}}>
        <li>Thời gian giao hàng có thể thay đổi tùy thuộc vào các yếu tố khách quan như thời tiết, địa chỉ giao hàng, ngày lễ…</li>
        <li>Trường hợp phát sinh chậm trễ trong việc giao hàng hoặc cung ứng dịch vụ, chúng tôi sẽ có thông tin kịp thời cho khách hàng và tạo cơ hội để khách hàng có thể hủy đơn nếu muốn.</li>
        <li>Để được tư vấn chi tiết hơn, quý khách vui lòng liên hệ với chúng tôi.</li>
      </ul>
      <Text>Shop Lucky Viet Nam cam kết mang đến cho quý khách hàng trải nghiệm mua sắm tuyệt vời nhất!</Text> */}
    </Container>
  );
};

export default ShippingMethod;
