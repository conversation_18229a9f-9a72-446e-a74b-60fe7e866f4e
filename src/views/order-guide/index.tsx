import { FC } from 'react';
import { Link } from 'react-router-dom';
import Container from '@/components/home/<USER>';
import { Text } from '@/components';
import { Grid } from 'antd';
import { useTranslation } from 'react-i18next';

const OrderGuide: FC = () => {
  const { t } = useTranslation();
  const ulStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
  };
  const contentStyle: React.CSSProperties = {
    padding: '5px',
  };
  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    textAlign: 'center',
  };
  const { md } = Grid.useBreakpoint();

  return (
    <Container
      style={{
        marginTop: !md ? 40 : 100,
        position: md ? 'unset' : 'relative',
        backgroundColor: '#FFFFFF',
      }}
    >
      <h1 style={{ ...titleStyle }}>{t('page.orderGuide.title.main')}</h1>
      <p>{t('page.orderGuide.title.sub')}</p>
      <p>{t('page.orderGuide.intro')}</p>

      <h4>{t('page.orderGuide.step1.title')}</h4>
      <p>{t('page.orderGuide.step1.title.sub')}</p>
      <ul>
        <li>{t('page.orderGuide.step1.method1')}</li>
        <li>{t('page.orderGuide.step1.method2')}</li>
      </ul>

      <h4>{t('page.orderGuide.step2.title')}</h4>
      <p>{t('page.orderGuide.step2.details')}</p>
      <ul>
        <li>{t('page.orderGuide.step2.check1')}</li>
        <li>{t('page.orderGuide.step2.check2')}</li>
        <li>{t('page.orderGuide.step2.check3')}</li>
        <li>{t('page.orderGuide.step2.check4')}</li>
        <li>{t('page.orderGuide.step2.check5')}</li>
      </ul>

      <h4>{t('page.orderGuide.step3.title')}</h4>
      <h4>{t('page.orderGuide.step4.title')}</h4>
      <h4>{t('page.orderGuide.step5.title')}</h4>
      <h4>{t('page.orderGuide.step6.title')}</h4>
      <p>{t('page.orderGuide.step6.note1')}</p>
      <p>{t('page.orderGuide.step6.note2')}</p>
      <p>{t('page.orderGuide.step6.payment')}</p>

      <h4>{t('page.orderGuide.step7.title')}</h4>
      <p>{t('page.orderGuide.step7.note')}</p>

      <h4>{t('page.orderGuide.step8.title')}</h4>
      <p>{t('page.orderGuide.step8.note')}</p>

      <p>{t('page.orderGuide.thankyou')}</p>
    </Container>
  );
};

export default OrderGuide;
