import { Icons, QuantityInput } from '@/components';
import CustomCheckbox from '@/components/common/CustomCheckbox';
import Container from '@/components/home/<USER>';
import PromoSection from '@/components/home/<USER>';
import { grayBg, white } from '@/constants/themeColor';
import { ROUTES } from '@/enums/Routes';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { CartItem } from '@/types/CartItem';
import { formatPriceVND, formatVND } from '@/utils/numberFormat';
import { DeleteOutlined } from '@ant-design/icons';
import {
  Breadcrumb,
  Button,
  Card,
  Col,
  Flex,
  Grid,
  Image,
  Row,
  Typography
} from 'antd';
import { CSSProperties, FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, Link, useNavigate } from 'react-router-dom';

const CartItems: FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { xs, sm, md } = Grid.useBreakpoint();
  const {
    cart,
    normal,
    ticket,
    handleRemove,
    handleUpdateCart,
    setNormal,
    startTransition,
  } = useGlobalContext();

  const [isAll, setAll] = useState<boolean>(false);

  const tList = cart.filter((i) => i.ticketId);
  const nList = cart.filter((i) => !i.ticketId);

  const breadcrumb = [
    {
      title: <Link to={ROUTES.HOME}>{t('header.home')}</Link>,
    },
    {
      title: (
        <Link
          to={ROUTES.PRODUCT_LIST}
          onClick={() => {
            const url = generatePath(ROUTES.PRODUCT_LIST, {
              categoryId: 'all',
            });
            navigate(url);
          }}
        >
          {t('header.products')}
        </Link>
      ),
    },
    {
      title: <div>{t('header.cart')}</div>,
    },
  ];

  const getIds = (list: CartItem[]) => list.map(({ id }) => id);

  const iconStyle: CSSProperties = {
    fontSize: 18,
  };

  const handleUpdate = (id: string, isChecked: boolean) => {
    setNormal((prev) => {
      if (isChecked) {
        return [...prev, id];
      }

      return prev.filter((_id) => _id !== id);
    });
  };

  const onDelete = (item: CartItem) => {
    handleRemove([item.id]);
    setNormal((prev) => prev.filter((_id) => _id !== item.id));
  };

  const onNavigateToHome = () =>
    startTransition(() => {
      navigate(ROUTES.HOME);
    });

  const EmptyView = (
    <Container style={{ marginTop: 100 }}>
      <Flex vertical align="center" gap="large">
        <Icons src="cart-empty" width={226} height={130} />
        <Typography.Text className="gray-darker">
          {t('cart.empty')}
        </Typography.Text>
        <Button
          onClick={onNavigateToHome}
          style={{
            marginTop: 32,
            width: 280,
            height: 40,
            color: '#0AAD0A',
            borderColor: '#0AAD0A',
          }}
        >
          {t('cart.shop_now')}
        </Button>
      </Flex>
    </Container>
  );

  const totalAmount = cart
    .filter(({ id }) => normal.includes(id))
    .reduce<number>(
      (prev, { price, quantity }) => prev + (price || 0) * quantity,
      0
    );

  const shippingFee = 10000;

  const onPurchase = () => {
    startTransition(() => {
      navigate(ROUTES.PRODUCT_ORDER);
    });
  };

  const renderList = (list: CartItem[]) => (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={24} md={16}>
        {/* Cart card */}
        <Card
          title={
            <CustomCheckbox
              checked={normal.length === nList.length}
              onChange={({ target }) => {
                setNormal(target.checked ? getIds(nList) : []);
              }}
            >
              <span style={{ fontWeight: 'bold' }}>
                {t('cart.choose_all').toUpperCase()}
              </span>
              <span style={{ fontWeight: 'normal' }}>
                {t('cart.all_products', { count: nList.length }).toUpperCase()}
              </span>
            </CustomCheckbox>
          }
        >
          {/* Cart items */}
          {list.map((item, index) => (
            <Row key={item.id}
              gutter={[16, 16]}
              style={{
                borderRadius: 16,
                backgroundColor: grayBg,
                marginBottom: index < list.length - 1 ? 16 : 0,
              }}
            >
              {/* Column 1 - Checkbox */}
              <Col
                md={1}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <CustomCheckbox
                  checked={[...normal].includes(item.id)}
                  onChange={({ target }) => {
                    handleUpdate(item.id, target.checked);
                  }}
                />
              </Col>

              {/* Column 2 - Item image */}
              <Col
                xs={7}
                md={4}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <Image
                  loading="lazy"
                  preview={false}
                  width={80}
                  height={80}
                  src={item?.cover}
                  style={{ borderRadius: 4 }}
                />
              </Col>

              {/* Column 3 */}
              {/* Medium and large screen */}
              {md && (
                <Col md={19}>
                  <Row gutter={[8, 8]}>
                    {/* Row 1 of Column 3 */}
                    <Col md={24}>
                      <div
                        style={{
                          fontSize: 14,
                          fontWeight: 'bold',
                          marginTop: 16,
                        }}
                      >
                        {item.name}
                      </div>
                    </Col>

                    {/* Row 2 of Column 3 */}
                    <Col md={24}>
                      <Row gutter={[8, 8]}>
                        <Col
                          md={6}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <div>
                            {t('cart.price', {
                              price: formatPriceVND((item?.price ?? 0).toFixed(2)),
                            })}
                          </div>
                        </Col>
                        <Col
                          md={14}
                          style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          <QuantityInput
                            defaultValue={item?.quantity ?? 1}
                            onChange={(quantity) =>
                              handleUpdateCart(
                                {
                                  ...item,
                                  quantity,
                                },
                                true
                              )
                            }
                            deleteFnc={onDelete.bind(null, item)}
                          />
                        </Col>
                        <Col
                          md={4}
                          style={{
                            display: 'flex',
                            justifyContent: 'end',
                            alignItems: 'center',
                          }}
                        >
                          <Typography.Text
                            className="cursor-pointer gray-darker no-wrap"
                            onClick={onDelete.bind(null, item)}
                          >
                            <DeleteOutlined style={iconStyle} />
                          </Typography.Text>
                        </Col>
                      </Row>
                    </Col>

                    {/* Row 3 of Column 3 */}
                    <Col
                      md={24}
                      style={{
                        display: 'flex',
                        justifyContent: 'end',
                      }}
                    >
                      <span style={{ marginRight: 6 }}>
                        {t('cart.temp_sum')}
                      </span>
                      <Typography.Text strong className="red-primary no-wrap">
                        {formatPriceVND(
                          ((item?.price ?? 0) * (item?.quantity ?? 0)).toFixed(
                            2
                          )
                        )}
                      </Typography.Text>
                    </Col>
                  </Row>
                </Col>
              )}
              {/* Mobile phone screen */}
              {xs && (
                <Col xs={10}>
                  <Row gutter={[8, 8]}>
                    {/* Row 1 of Column 3 */}
                    <Col xs={24}>
                      <div
                        style={{
                          fontSize: 10,
                          fontWeight: 'bold',
                          marginTop: 16,
                        }}
                      >
                        {item.name}
                      </div>
                    </Col>

                    {/* Row 2 of Column 3 */}
                    <Col
                      md={24}
                      style={{
                        display: 'flex',
                        justifyContent: 'end',
                        fontSize: 10,
                      }}
                    >
                      <div>
                        {t('cart.price', {
                          price: formatPriceVND((item?.price ?? 0).toFixed(2)),
                        })}
                      </div>
                    </Col>
                  </Row>
                </Col>
              )}
              {/* Column 4 - mobile screen */}
              {xs && (
                <Col
                  xs={4}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    paddingLeft: 0,
                  }}
                >
                  <QuantityInput
                    defaultValue={item?.quantity ?? 1}
                    onChange={(quantity) =>
                      handleUpdateCart(
                        {
                          ...item,
                          quantity,
                        },
                        true
                      )
                    }
                    isMobileSize={true}
                    style={{ marginBottom: 8 }}
                    deleteFnc={onDelete.bind(null, item)}
                  />
                  <Typography.Text
                    className="cursor-pointer gray-darker no-wrap"
                    onClick={onDelete.bind(null, item)}
                    style={{ display: 'block' }}
                  >
                    <DeleteOutlined style={iconStyle} />
                  </Typography.Text>
                </Col>
              )}
            </Row>
          ))}
        </Card>
      </Col>
      {/* Cart info and payment button */}
      <Col xs={24} sm={24} md={8}>
        <Card title={t('cart.order_info').toUpperCase()}>
          <Row gutter={[8, 8]} align="middle">
            <Col span={14}>
              <span>{t('cart.total_temp', { count: list.length })}</span>
            </Col>
            <Col span={10} style={{ justifyContent: 'end', display: 'flex' }}>
              <Typography.Text>
                {formatPriceVND(totalAmount.toFixed(2))}
              </Typography.Text>
            </Col>
          </Row>

          <Row gutter={[8, 8]} align="middle">
            <Col span={14}>
              <span>{t('cart.shipping')}</span>
            </Col>
            <Col span={10} style={{ justifyContent: 'end', display: 'flex' }}>
              <Typography.Text>
                {formatPriceVND(shippingFee.toFixed(2))}
              </Typography.Text>
            </Col>
          </Row>

          <Row gutter={[8, 8]} align="middle">
            <Col span={14}>
              <span>{t('cart.total')}</span>
            </Col>
            <Col span={10} style={{ justifyContent: 'end', display: 'flex' }}>
              <Typography.Text strong className="red-primary no-wrap">
                {formatPriceVND((totalAmount + shippingFee).toFixed(2))}
              </Typography.Text>
            </Col>
          </Row>

          <Row gutter={[8, 8]} align="middle">
            <Col span={24} style={{ justifyContent: 'end', display: 'flex' }}>
              <span>{t('cart.vat')}</span>
            </Col>
          </Row>
          <Button
            style={{
              fontSize: !xs ? 16 : 14,
              color: white,
              fontWeight: 'bolder',
            }}
            className={"checkout-button" + (normal.length ? "" : " disabled")}
            variant="solid"
            block
            disabled={!normal.length}
            onClick={onPurchase}
          >
            {t('cart.payment').toUpperCase()}
          </Button>
        </Card>
      </Col>
    </Row>
  );

  const ListItems = (
    <Flex vertical gap="middle">
      <div style={{ borderRadius: 8 }}>
        <Flex vertical gap="middle">
          {renderList(nList)}
        </Flex>
      </div>
    </Flex>
  );

  return (
    <Container style={{ marginTop: md ? 120 : 50,  position: md ? 'unset': 'relative'  }}>
      {!md && <PromoSection/>}
      <Breadcrumb items={breadcrumb} style={{ marginBottom: 10 }} />
      {cart.length ? ListItems : EmptyView}
    </Container>
  );
};

export default CartItems;
