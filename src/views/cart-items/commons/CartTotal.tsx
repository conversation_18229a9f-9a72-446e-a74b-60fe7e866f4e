import { ROUTES } from '@/enums/Routes';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { formatVND } from '@/utils/numberFormat';
import { Button, Flex, Grid, Typography } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const FooterStyles: CSSProperties = {
  height: 112,
  padding: '15px 5vw',
  gap: 50,
  background: '#fff',
  display: 'flex',
  justifyContent: 'flex-end',
  alignItems: 'baseline',
};

const textPrice: CSSProperties = {
  fontSize: 20,
  fontWeight: 'bold',
};

const CartTotal: FC<{ isTicket: boolean }> = ({ isTicket }) => {
  const { t } = useTranslation();
  const { cart, normal, ticket, startTransition } = useGlobalContext();
  const { md } = Grid.useBreakpoint();
  const navigate = useNavigate();

  const items = (isTicket ? ticket : normal) || [];

  const totalAmount = cart
    .filter(({ id, ticketId }) => items.includes([id, ticketId].join('-')))
    .reduce<number>(
      (prev, { price, quantity }) => prev + (price || 0) * quantity,
      0
    );

  const onPurchase = () => {
    startTransition(() => {
      navigate(ROUTES.PRODUCT_ORDER);
    });
  };

  return (
    <Flex
      justify="end"
      align={!md ? 'start' : 'center'}
      gap={10}
      style={{ flexDirection: !md ? 'column' : 'row' }}
    >
      <div>
        <Typography.Text className="gray-darker" style={{ ...textPrice }}>
          {t('footer.total')}
        </Typography.Text>
        <Typography.Text
          className="red-primary"
          style={{ ...textPrice, marginLeft: 10 }}
        >
          {formatVND(totalAmount.toFixed(2))}
        </Typography.Text>
      </div>
      <Button
        danger
        type="primary"
        disabled={!items.length}
        onClick={onPurchase}
        style={{
          width: 240,
          height: 50,
        }}
      >
        {t('button.purchase')}
      </Button>
    </Flex>
  );
};

export default CartTotal;
