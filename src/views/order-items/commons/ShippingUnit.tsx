import { Container, Icons } from '@/components';
import { formatVND } from '@/utils/numberFormat';
import {
  Button,
  Flex,
  Input,
  Modal,
  Radio,
  RadioChangeEvent,
  Typography,
} from 'antd';
import { FC, memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

export type UnitType = {
  id: string;
  name: string;
  price: number;
  note?: string;
};

interface ShippingUnitProps {
  onChange: (unit: UnitType) => void;
}

const units: UnitType[] = [
  {
    id: '1',
    name: 'Ahaha',
    price: 12000,
  },
  {
    id: '2',
    name: 'Ahih<PERSON>',
    price: 42000,
  },
  {
    id: '3',
    name: 'J&T',
    price: 12000000,
  },
];

const ShippingUnit: FC<ShippingUnitProps> = ({ onChange }) => {
  const { t } = useTranslation();

  const [isOpen, setOpen] = useState<boolean>(false);
  const [selected, setSelected] = useState<UnitType>(units[0]);

  const onUnitChange = (e: RadioChangeEvent) => {
    setSelected(e.target.value);
  };

  const handleSubmit = () => {
    setOpen(false);
  };

  return (
    <>
      <Modal
        title={t('order.shipping_unit')}
        closable={false}
        maskClosable={false}
        open={isOpen}
        footer={[
          <Button key="back" onClick={setOpen.bind(null, false)}>
            Return
          </Button>,
        ]}
      >
        <Radio.Group
          onChange={onUnitChange}
          value={selected}
          style={{ paddingLeft: 10, paddingRight: 10 }}
        >
          <Flex vertical gap="middle">
            {units.map((u) => (
              <Radio key={u.id} value={u}>
                {u.name}
              </Radio>
            ))}
          </Flex>
        </Radio.Group>
      </Modal>
      <Container>
        <Flex align="center" gap="large" style={{ marginBottom: 25 }}>
          <Icons src="shipping-order" size={40} />
          <Typography.Text style={{ fontSize: 18 }}>
            Đơn vị vận chuyển
          </Typography.Text>
        </Flex>
        <Flex align="center" justify="space-between" gap={50}>
          <Typography.Text strong className="no-wrap">
            {selected.name}
          </Typography.Text>
          <Typography.Text
            strong
            className="green-primary cursor-pointer no-wrap"
            onClick={setOpen.bind(null, true)}
          >
            Thay đổi
          </Typography.Text>
          <Input
            disabled
            placeholder={selected.note || t('order.note_to_seller')}
            variant="filled"
          />
          <Typography.Text strong className="red-primary no-wrap">
            {formatVND(selected.price.toFixed(2))}
          </Typography.Text>
        </Flex>
      </Container>
    </>
  );
};

export default memo(ShippingUnit);
