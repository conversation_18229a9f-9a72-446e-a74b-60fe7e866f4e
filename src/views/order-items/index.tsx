import { QuantityInput, Text } from '@/components';
import Container from '@/components/home/<USER>';
import { grayBg, white } from '@/constants/themeColor';
import { ROUTES } from '@/enums/Routes';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { useAuthStore } from '@/stores/authStore';
import { CartItem } from '@/types/CartItem';
import { formatPriceVND, formatVND } from '@/utils/numberFormat';
import {
  Breadcrumb,
  Button,
  Card,
  Col,
  Flex,
  Grid,
  Image,
  Input,
  Radio,
  RadioChangeEvent,
  Row,
  Typography,
} from 'antd';
import { CSSProperties, FC, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import '@/components/common/CustomRadio.css';
import authMembersService from '@/services/auth-members.service';
import { MyAddressDto } from '@/dto/auth-member.dto';
import { toastService } from '@/services/@common';
import ordersService from '@/services/orders.service';
import { CreateOrderItemReq, CreateOrderReq } from '@/dto/order.dto';
import PromoSection from '@/components/home/<USER>';
import { NSOrder } from '@/enums/NSOrder';

export type WalletType = {
  id: string;
  code: NSOrder.EPaymentMethod;
  name: string;
  balancePropName: string;
  note?: string;
};

const img =
  'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png';

const OrderItems: FC = () => {
  const { t } = useTranslation();

  const { member } = useAuthStore();
  const [addressInput, setAddressInput] = useState('');
  const payments: WalletType[] = [
    {
      id: '1',
      code: NSOrder.EPaymentMethod.COD,
      name: t('order.payment_methods.cod'),
      balancePropName: 'balance',
    },
    {
      id: '2',
      code: NSOrder.EPaymentMethod.BANK_TRANSFER,
      name: t('order.payment_methods.bank'),
      balancePropName: 'balance',
    },
  ];

  const [selected, setSelected] = useState<WalletType>(payments[0]);

  const onMethodChange = (e: RadioChangeEvent) => {
    const selectedPayment = payments.find(
      (payment) => payment.id === e.target.value
    );
    if (selectedPayment) {
      setSelected(selectedPayment);
    }
  };

  const breadcrumb = [
    {
      title: <Link to={ROUTES.HOME}>{t('header.home')}</Link>,
    },
    {
      title: <Link to={ROUTES.PRODUCT_LIST}>{t('header.products')}</Link>,
    },
    {
      title: <Link to={ROUTES.PRODUCT_CART}>{t('header.cart')}</Link>,
    },
    {
      title: <div>{t('header.order')}</div>,
    },
  ];

  const navigate = useNavigate();
  const { xs, sm, md } = Grid.useBreakpoint();
  const { cart, normal, ticket, handleEmptyCart, startTransition } =
    useGlobalContext();

  const [isAll, setAll] = useState<boolean>(false);

  const tList = cart.filter((i) => i.ticketId);
  const nList = cart.filter((i) => !i.ticketId);

  const getIds = (list: CartItem[]) => list.map(({ id }) => id);

  const iconStyle: CSSProperties = {
    fontSize: 18,
  };

  const onNavigateToHome = () =>
    startTransition(() => {
      navigate(ROUTES.HOME);
    });

  const totalAmount = nList
    .filter(({ id }) => normal.includes(id))
    .reduce<number>(
      (prev, { price, quantity }) => prev + (price || 0) * quantity,
      0
    );

  const [address, setAddress] = useState<MyAddressDto | null>(null);
  const getAddress = useCallback(async () => {
    try {
      const res = (
        await authMembersService.myAddress({ pageIndex: 1, pageSize: 10 })
      ).data.find((item) => item.isDefault);
      setAddress(res);
    } catch (error) {
      setAddress(null);
    }
  }, []);

  useEffect(() => {
    getAddress();
  }, [getAddress]);

  const [addressList, setAddressList] = useState<MyAddressDto[]>([]);

  const getAddressList = useCallback(async () => {
    try {
      const res = (
        await authMembersService.myAddress({ pageIndex: 1, pageSize: 10 })
      ).data;
      setAddressList(res);
    } catch (error) {
      setAddressList([]);
    }
  }, []);

  useEffect(() => {
    getAddressList();
  }, [getAddressList]);

  const [showList, setShowList] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<string | null>(null);

  const handleToggleList = () => {
    setShowList(!showList);
    setSelectedAddress(address?.id || null);
  };

  const handleChange = (e) => {
    const newAddress = addressList.find((item) => item.id === e.target.value);
    if (newAddress) {
      setAddress(newAddress);
      setSelectedAddress(newAddress.id);
    }
  };

  const [showInput, setShowInput] = useState(false);
  const [newAddress, setNewAddress] = useState('');
  const newAddressDto: MyAddressDto = {
    address: newAddress,
    isDefault: true,
    provinceCode: null,
    districtCode: null,
    wardCode: null,
  };
  // const handleAddAddress = async () => {
  //   if (!newAddress.trim()) return;
  //   try {
  //     const response = await authMembersService.addMyAddress(newAddressDto);
  //     if (response) {
  //       setAddress(newAddressDto);
  //       setShowInput(false);
  //     }
  //   } catch (error) {
  //     console.error('Lỗi cập nhật địa chỉ:', error);
  //   }
  // };

  const shippingFee = 10000;

  const onPurchase = async () => {
    try {
      const items: CreateOrderItemReq[] = nList
        .filter(({ id }) => normal.includes(id))
        .map((item) => ({ productId: item.id, qty: item.quantity }));
      const req: CreateOrderReq = {
        listOrderItem: [...items],
        paymentMethod: selected.code,
        deliveryAddress: showInput ? addressInput : address?.address || '',
        totalAmount: totalAmount + shippingFee,
        returnUrl: `${window.location.origin}/payment-success`,
      };
      const res = await ordersService.createOrder(req);
      if (res?.paymentUrl) {
        window.location.href = res.paymentUrl;
      } else {
        handleEmptyCart();
        onNavigateToHome();
        toastService.success(t('order.payment_success'));
      }
    } catch (error) {
      toastService.handleError(error, t('error.unauthorized'));
      console.log(`-------------------`);
      console.log({ error });
      console.log(`-------------------`);
    }
  };

  const renderList = (list: CartItem[]) => (
    <Row gutter={[8, 8]}>
      <Col xs={24} sm={24} md={16}>
        {/* Cart card */}
        <Card>
          {/* Cart items */}
          {list.map((item, index) => (
            <Row
              key={item.id}
              gutter={[16, 16]}
              style={{
                borderRadius: 16,
                backgroundColor: grayBg,
                marginBottom: index < list.length - 1 ? 16 : 0,
              }}
            >
              {/* Column 1 - Checkbox */}
              <Col
                md={1}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              ></Col>

              {/* Column 2 - Item image */}
              <Col
                xs={7}
                md={4}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <Image
                  loading="lazy"
                  preview={false}
                  width={80}
                  height={80}
                  src={item?.cover}
                  style={{ borderRadius: 4 }}
                />
              </Col>

              {/* Column 3 */}
              {/* Medium and large screen */}
              {md && (
                <Col md={19}>
                  <Row gutter={[8, 8]}>
                    {/* Row 1 of Column 3 */}
                    <Col md={24}>
                      <div
                        style={{
                          fontSize: 14,
                          fontWeight: 'bold',
                          marginTop: 16,
                        }}
                      >
                        {item.name}
                      </div>
                    </Col>

                    {/* Row 2 of Column 3 */}
                    <Col md={24}>
                      <Row gutter={[8, 8]}>
                        <Col
                          md={6}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <div>
                            {t('cart.price', {
                              price: formatPriceVND(
                                (item?.price ?? 0).toFixed(2)
                              ),
                            })}
                          </div>
                        </Col>
                        <Col
                          md={14}
                          style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          <QuantityInput
                            defaultValue={item?.quantity ?? 1}
                            isAddon={false}
                            disabled={true}
                          />
                        </Col>
                      </Row>
                    </Col>

                    {/* Row 3 of Column 3 */}
                    <Col
                      md={24}
                      style={{
                        display: 'flex',
                        justifyContent: 'end',
                      }}
                    >
                      <span style={{ marginRight: 6 }}>
                        {t('cart.temp_sum')}
                      </span>
                      <Typography.Text strong className="red-primary no-wrap">
                        {formatPriceVND(
                          ((item?.price ?? 0) * (item?.quantity ?? 0)).toFixed(
                            2
                          )
                        )}
                      </Typography.Text>
                    </Col>
                  </Row>
                </Col>
              )}
              {/* Mobile phone screen */}
              {xs && (
                <Col xs={10}>
                  <Row gutter={[8, 8]}>
                    {/* Row 1 of Column 3 */}
                    <Col xs={24}>
                      <div
                        style={{
                          fontSize: 10,
                          fontWeight: 'bold',
                          marginTop: 16,
                        }}
                      >
                        {item.name}
                      </div>
                    </Col>

                    {/* Row 2 of Column 3 */}
                    <Col
                      md={24}
                      style={{
                        display: 'flex',
                        justifyContent: 'end',
                        fontSize: 10,
                      }}
                    >
                      <div>
                        {t('cart.price', {
                          price: formatPriceVND((item?.price ?? 0).toFixed(2)),
                        })}
                      </div>
                    </Col>
                  </Row>
                </Col>
              )}
              {/* Column 4 - mobile screen */}
              {xs && (
                <Col
                  xs={4}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    paddingLeft: 0,
                  }}
                >
                  <QuantityInput
                    defaultValue={item?.quantity ?? 1}
                    isMobileSize={true}
                    isAddon={false}
                    disabled={true}
                    style={{ marginBottom: 8 }}
                  />
                </Col>
              )}
            </Row>
          ))}
        </Card>
      </Col>
      <Col xs={24} sm={24} md={8}>
        {/* Payment methods */}
        <Card
          title={t('order.payment_method').toUpperCase()}
          bodyStyle={{ padding: 0 }}
        >
          <Radio.Group
            onChange={onMethodChange}
            value={selected.id} // Value here should be a WalletType object
            style={{ width: '100%' }}
          >
            <Flex vertical gap="middle">
              {payments.map((u) => (
                <Radio className="custom-radio" key={u.id} value={u.id}>
                  {u.name}
                </Radio>
              ))}
            </Flex>
          </Radio.Group>
          {/* {selected.id === '2' && (
          <div
            style={{
              textAlign: "center",
              padding: 20,
              border: "1px solid #ddd",
              borderRadius: 10,
              backgroundColor: "#ffffff",
              margin: '0 auto'
            }}
          >
            <h4 style={{ color: "#0056b3", marginBottom: 5 }}>
              CTY TNHH DV TM CÔNG NGHỆ HAPPY SHOP
            </h4>
            <p style={{ fontSize: 16, fontWeight: "bold", margin: "5px 0" }}>
              ************
            </p>
            <p style={{ margin: "0", color: "#666" }}>
              Ngân hàng TMCP Công Thương Việt Nam
              <br />
              CN TP HCM - HOI SO
            </p>
            <h4 style={{ color: "#d10000", marginBottom: 10 }}>VietinBank eFAST</h4>
            <div
              style={{
                border: "1px solid #0056b3", 
                padding: "5px",
                display: "inline-block",
                borderRadius: "12px", 
              }}
            >
              <img
                src="/images/qr.jpg"
                alt="QR Code"
                style={{
                  width: "200px",
                  height: "200px",
                  display: "block",
                  backgroundColor: "#fff",
                  borderRadius: "10px", 
                }}
              />
            </div>

            <p style={{ color: "#666", fontSize: 14 }}>
              Mã QR để nhận tiền qua chuyển khoản nhanh
            </p>
            <div style={{ display: "flex", justifyContent: "center",alignItems: 'center', gap: 10 }}>
              <img
                src="/images/napas247.png"
                alt="Napas 247"
                style={{ height: 20 }}
              />
              <img
                src="/images/vietqr.png"
                alt="VietQR"
                style={{ height: 20, marginBottom: 5 }}
              />
            </div>
          </div>
        )} */}
        </Card>
        {/* Delivery info */}
        <Card title={t('order.delivery.title').toUpperCase()}>
          <Row gutter={[8, 8]} align="middle">
            <Col span={14}>
              <span>
                {t('order.delivery.receiver', { count: list.length })}
              </span>
            </Col>
            <Col span={10} style={{ justifyContent: 'end', display: 'flex' }}>
              <Typography.Text>{member?.firstName}</Typography.Text>
            </Col>
          </Row>

          <Row gutter={[8, 8]} align="middle">
            <Col span={14}>
              <span>{t('order.delivery.phone')}</span>
            </Col>
            <Col span={10} style={{ justifyContent: 'end', display: 'flex' }}>
              <Typography.Text>{member?.phone}</Typography.Text>
            </Col>
          </Row>

          <Row gutter={[8, 8]}>
            <Col span={6}>
              <span>{t('order.delivery.address')}</span>
            </Col>
            <Col
              span={18}
              style={{
                justifyContent: 'end',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'top',
                textAlign: 'right',
              }}
            >
              {address ? (
                <>
                  {showInput ? (
                    <Input
                      placeholder={t('order.delivery.enter_address')}
                      value={newAddress}
                      onChange={(e) => setNewAddress(e.target.value)}
                    />
                  ) : (
                    <>
                      <Typography.Text>{address?.address}</Typography.Text>
                      <Typography.Text
                        style={{ color: 'blue', cursor: 'pointer' }}
                        onClick={() => setShowInput(true)}
                      >
                        {t('order.change_address')}
                      </Typography.Text>
                    </>
                  )}
                  {/* <Typography.Text>{address?.address}</Typography.Text>Í */}
                </>
              ) : (
                <Typography.Text
                  style={{ color: 'blue', cursor: 'pointer' }}
                  onClick={() => setShowInput(true)}
                >
                  {t('order.add_address')}
                </Typography.Text>
              )}
            </Col>
            {/* Input nhập địa chỉ mới */}
            {/* {showInput && (
              <Col style={{ width: '100%', marginTop: 8 }}>
                <Input
                  placeholder="Nhập địa chỉ mới"
                  value={newAddress}
                  onChange={(e) => setNewAddress(e.target.value)}
                  onPressEnter={handleAddAddress}
                />
                <Button
                  type="primary"
                  style={{ marginTop: 8 }}
                  onClick={handleAddAddress}
                >
                  Cập nhật
                </Button>
              </Col>
            )}
            <Col style={{ width: '100%' }}>
              {showList && (
                <Radio.Group
                  onChange={handleChange}
                  value={selectedAddress}
                  style={{ paddingLeft: 10, paddingRight: 10, width: '100%' }}
                >
                  <Flex vertical gap="middle">
                    {addressList.map((i) => (
                      <Radio className="custom-radio" key={i.id} value={i.id}>
                        {i?.address}
                      </Radio>
                    ))}
                  </Flex>
                </Radio.Group>
              )}
            </Col> */}
          </Row>
        </Card>
        {/* Cart info and payment button */}
        <Card title={t('cart.order_info').toUpperCase()}>
          <Row gutter={[8, 8]} align="middle">
            <Col span={14}>
              <span>{t('cart.total_temp', { count: list.length })}</span>
            </Col>
            <Col span={10} style={{ justifyContent: 'end', display: 'flex' }}>
              <Typography.Text>
                {formatPriceVND(totalAmount.toFixed(2))}
              </Typography.Text>
            </Col>
          </Row>

          <Row gutter={[8, 8]} align="middle">
            <Col span={14}>
              <span>{t('cart.shipping')}</span>
            </Col>
            <Col span={10} style={{ justifyContent: 'end', display: 'flex' }}>
              <Typography.Text>
                {formatPriceVND(shippingFee.toFixed(2))}
              </Typography.Text>
            </Col>
          </Row>

          <Row gutter={[8, 8]} align="middle">
            <Col span={14}>
              <span>{t('cart.total')}</span>
            </Col>
            <Col span={10} style={{ justifyContent: 'end', display: 'flex' }}>
              <Typography.Text strong className="red-primary no-wrap">
                {formatPriceVND((totalAmount + shippingFee).toFixed(2))}
              </Typography.Text>
            </Col>
          </Row>

          <Row gutter={[8, 8]} align="middle">
            <Col span={24} style={{ justifyContent: 'end', display: 'flex' }}>
              <span>{t('cart.vat')}</span>
            </Col>
          </Row>
          <Button
            style={{
              fontSize: !xs ? 16 : 14,
              color: white,
              fontWeight: 'bolder',
            }}
            className="checkout-button"
            variant="solid"
            block
            onClick={onPurchase}
          >
            {t('cart.payment').toUpperCase()}
          </Button>
        </Card>
      </Col>
    </Row>
  );

  const ListItems = (
    <Flex vertical gap="middle">
      <div style={{ borderRadius: 8 }}>
        <Flex vertical gap="middle">
          {renderList(nList.filter(({ id }) => normal.includes(id)))}
        </Flex>
      </div>
    </Flex>
  );

  return (
    <Container
      style={{
        marginTop: md ? 120 : 40,
        position: md ? 'unset' : 'relative',
        backgroundColor: '#FFFFFF',
      }}
    >
      {!md && <PromoSection />}
      <Breadcrumb items={breadcrumb} style={{ marginBottom: 10 }} />
      {ListItems}
    </Container>
  );
};

export default OrderItems;
