import { FC, useCallback, useEffect } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import Container from '@/components/home/<USER>';
import { Icons } from '@/components';
import {
  Button,
  Card,
  Col,
  Descriptions,
  Divider,
  Flex,
  Row,
  Table,
  Typography,
} from 'antd';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { t } from 'i18next';
import { ROUTES } from '@/enums/Routes';
import { useOrderDetail } from './hooks/useOrderDetail';
import { useOrderDetailByPaymentTransaction } from './hooks/useOrderTransaction';
import { formatPriceVND } from '@/utils/numberFormat';

const { Title, Text } = Typography;

const PaymentSuccess: FC = () => {
  const navigate = useNavigate();
  const { handleEmptyCart, startTransition } = useGlobalContext();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const paramValue = queryParams.get('vnp_TxnRef');
  const { data: orderDetail } = useOrderDetailByPaymentTransaction({
    id: paramValue,
  });

  const onNavigateToHome = useCallback(
    () =>
      startTransition(() => {
        navigate(ROUTES.HOME);
      }),
    [navigate, startTransition]
  );

  const onNavigateToCart = useCallback(
    () =>
      startTransition(() => {
        navigate(ROUTES.PRODUCT_CART);
      }),
    [navigate, startTransition]
  );
  const [searchParams] = useSearchParams();
  const rspCode = searchParams.get('vnp_ResponseCode') || '';

  useEffect(() => {
    if (rspCode === '00') {
      handleEmptyCart();
    }
  }, [handleEmptyCart, rspCode]);

  const orderNumber = Math.floor(Math.random() * 10000000) + 1;

  const renderSuccess = useCallback(() => {
    if (rspCode === '00') {
      return (
        <Row gutter={[16, 16]}>
          <Col xs={24} md={24}>
            <Card bordered>
              <Text type="success">
                Cảm ơn bạn. Đơn hàng của bạn đã được nhận.
              </Text>
              <Descriptions column={1} style={{ marginTop: 16 }}>
                <Descriptions.Item label="Mã đơn hàng">
                  {orderDetail?.id}
                </Descriptions.Item>
                <Descriptions.Item label="Người nhận">
                  {orderDetail?.member?.firstName}
                </Descriptions.Item>
                <Descriptions.Item label="Số điện thoại">
                  {orderDetail?.member?.phone}
                </Descriptions.Item>
                <Descriptions.Item label="Email">
                  {orderDetail?.member?.email}
                </Descriptions.Item>
                <Descriptions.Item label="Ngày">
                  {orderDetail?.createdDate}
                </Descriptions.Item>
                <Descriptions.Item label="Địa chỉ">
                  {/* {orderDetail.deliveryAddress} */}
                  Chung cư An Phú Đông, Phường An Phú Đông, Quận 12, TP. HCM
                </Descriptions.Item>
                <Descriptions.Item label="Tổng cộng">
                  <strong>{formatPriceVND(orderDetail?.totalAmount)}</strong>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>
      );
    }
    return (
      <>
        <Flex vertical align="center" gap="large">
          <Icons src="payment" width={226} height={130} />
          <Typography.Text className="gray-darker">
            {t('order.failed')}
          </Typography.Text>
          <Button
            onClick={onNavigateToCart}
            danger
            style={{
              marginTop: 32,
              width: 280,
              height: 40,
            }}
          >
            {t('order.payment_again')}
          </Button>
        </Flex>
      </>
    );
  }, [onNavigateToCart, onNavigateToHome, rspCode, orderDetail]);
  return <Container style={{ marginTop: 100 }}>{renderSuccess()}</Container>;
};

export default PaymentSuccess;
