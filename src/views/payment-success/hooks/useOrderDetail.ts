import { OrderDetailDto, OrderDetailReq } from '@/dto/order.dto';
import ordersService from '@/services/orders.service';
import { useCallback, useEffect, useState } from 'react';

export const useOrderDetail = ({ id }: OrderDetailReq) => {
  const [data, setData] = useState<OrderDetailDto>();

  const orderDetail = useCallback(async () => {
    try {
      const res: OrderDetailDto = await ordersService.detail({
        id,
      });
      setData(res);
    } catch (error) {}
  }, [id]);
  useEffect(() => {
    orderDetail();
  }, [orderDetail]);

  return {
    data,
  };
};
