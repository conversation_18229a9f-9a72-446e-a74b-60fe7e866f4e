import { OrderDetailDto, OrderDetailReq } from '@/dto/order.dto';
import ordersService from '@/services/orders.service';
import { useCallback, useEffect, useState } from 'react';

export const useOrderDetailByPaymentTransaction = ({ id }: OrderDetailReq) => {
  const [data, setData] = useState<OrderDetailDto>();

  const orderDetail = useCallback(async () => {
    try {
      const res: OrderDetailDto = await ordersService.detailByPaymentTransaction({
        id,
      });
      setData(res);
    } catch (error) {}
  }, [id]);
  useEffect(() => {
    orderDetail();
  }, [orderDetail]);

  return {
    data,
  };
};
