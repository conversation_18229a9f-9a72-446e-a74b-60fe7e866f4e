import categoryService from '@/services/category.service';
import { useCallback, useEffect, useState } from 'react';

export const useListCategory = () => {
  const [data, setData] = useState([]);
  const loadData = useCallback(async () => {
    try {
      const res = await categoryService.list({
        pageIndex: 1,
        pageSize: 50,
      });
      setData(res?.data || []);
    } catch (error) {}
  }, []);
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
  };
};
