import { white } from '@/constants/themeColor';
import { FC } from 'react';
import Slider from 'react-slick';
import RevealItem from '../../components/home/<USER>';
import Photo from '/images/latest-reveal-cover.png';
import { useTranslation } from 'react-i18next';
import { Grid } from 'antd';
import { useLatestReveal } from "./hooks/useLatestReveal";

interface ILatestRevealProps { }



const LatestReveal: FC<ILatestRevealProps> = () => {


  const { t } = useTranslation();
  const { xl } = Grid.useBreakpoint();

  const { data } = useLatestReveal()
  const responsive = [
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 1280,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 1,
      },
    },

    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 800,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },

    {
      breakpoint: 700,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },
  ];

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 6,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3500,
    responsive,
    arrows: false,
  };

  return (
    <>
      {/* <div
        style={{
          background: `url(${Photo}) no-repeat`,
          backgroundSize: '100%  100%',
          width: xl ? 288 : 200,
          height: xl ? 83 : 60,
          fontWeight: 400,
          fontSize: xl ? 28 : 20,
          fontFamily: 'Baloo Bhai',
          color: white,
          paddingLeft: xl ? 20 : 10,
          lineHeight: xl ? '83px' : '60px',
          transform: 'translate(-20px ,-10px)',
        }}
      >
        {t('home.latestReveal')}
      </div> */}
      <Slider {...settings}>
        {data.map((item, index) => (
          <div style={{ background: white }} key={index}>
            <RevealItem
              item={item}
            />
          </div>
        ))}
      </Slider>
    </>
  );
};

export default LatestReveal;
