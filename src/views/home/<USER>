import { white } from '@/constants/themeColor';
import { FC } from 'react';
import Slider from 'react-slick';
import ComingSoonItem from '../../components/home/<USER>';
import { useComingSoon } from './hooks/useComingSoon';
import { Col, Row } from 'antd';
interface IComingSoonProps {}

const ComingSoon: FC<IComingSoonProps> = () => {
  const { data } = useComingSoon();
  const responsive = [
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 1280,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 1,
      },
    },

    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 800,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },

    {
      breakpoint: 700,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },
  ];

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2500,
    responsive,
    arrows: false,
  };

  return (
    <Slider {...settings}>
      {data.map((item, index) => (
        <div style={{ background: white }} key={index}>
          <ComingSoonItem item={item} />
        </div>
      ))}
    </Slider>
  );
};

export default ComingSoon;
