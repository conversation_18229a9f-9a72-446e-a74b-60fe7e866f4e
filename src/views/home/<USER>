import Banner from '@/components/home/<USER>';
import CategoriesBySvg from '@/components/home/<USER>';
import ComingSoon from '@/views/home/<USER>';
import Container from '@/components/home/<USER>';
import LatestReveal from '@/views/home/<USER>';
import TopSection from '@/components/home/<USER>';
import Trends from '@/components/home/<USER>';
import ProductItem from '@/components/product/ProductItem';
import { textColorBase, white } from '@/constants/themeColor';
import { FireOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Grid, Row, Tabs } from 'antd';
import { CSSProperties, FC } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, useNavigate } from 'react-router-dom';
import { useListCategory } from './hooks/useListCategory';
import { useListProducts } from '../search-menu/hooks/useListProducts';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { ROUTES } from '@/enums/Routes';
import PromoSection from '@/components/home/<USER>';
import BannerMob from '@/components/home/<USER>';
import ProductCards from '@/components/home/<USER>';

const leftStyles: React.CSSProperties = {
  color: textColorBase,
  fontWeight: 'bold',
  fontSize: 20,
  margin: '10px 0',
  paddingLeft: 5,
};

const wrapperStyles: React.CSSProperties = {
  // background: white,
  // margin: '20px 0',
  padding: '16px 20px',
  borderRadius: 8,
};

const tabsStyles: React.CSSProperties = {
  color: textColorBase,
  fontSize: 20,
  margin: '10px 0',
  paddingLeft: 5,
};

const Home: FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { md, xs, xl } = Grid.useBreakpoint();
  const { data: listCategory } = useListCategory();
  const { data: listProducts } = useListProducts();
  const { startTransition } = useGlobalContext();

  const onNavigate = (url: string) => {
    startTransition(() => {
      navigate(url);
    });
  };

  const leftTitleStyles: CSSProperties = {
    fontWeight: 400,
    fontSize: xl ? 26 : 20,
    margin: '10px 0',
    fontFamily: 'Baloo Bhai',
  };

  const comingSoonStyles: React.CSSProperties = {
    color: '#FF0909',
    ...leftTitleStyles,
  };

  const trendsStyles: React.CSSProperties = {
    color: '#1890FF',
    ...leftTitleStyles,
  };

  // const tabItems = [
  //   {
  //     label: `Recommend`,
  //     key: '1',
  //     children: (
  //       <>
  //         <Row gutter={md ? [12, 16] : [10, 10]}>
  //           {products?.map((product, index) => (
  //             <Col key={index} xs={12} sm={12} md={8} lg={6} xl={6} xxl={4}>
  //               {/* <ProductItem item={{ ...product }} /> */}
  //             </Col>
  //           ))}
  //         </Row>
  //       </>
  //     ),
  //   },
  //   {
  //     label: `Virtual coupons`,
  //     key: '2',
  //     children: (
  //       <>
  //         <Row gutter={md ? [12, 16] : [10, 10]}>
  //           {products?.map((product, index) => (
  //             <Col key={index} xs={12} sm={12} md={8} lg={6} xl={6} xxl={4}>
  //               {/* <ProductItem item={{ ...product }} /> */}
  //             </Col>
  //           ))}
  //         </Row>
  //       </>
  //     ),
  //   },
  //   {
  //     label: `Vehicle`,
  //     key: '3',
  //     children: (
  //       <>
  //         <Row gutter={md ? [12, 16] : [10, 10]}>
  //           {products?.map((product, index) => (
  //             <Col key={index} xs={12} sm={12} md={8} lg={6} xl={6} xxl={4}>
  //               {/* <ProductItem item={{ ...product }} /> */}
  //             </Col>
  //           ))}
  //         </Row>
  //       </>
  //     ),
  //   },
  //   {
  //     label: `Watches and jewelry`,
  //     key: '4',
  //     children: (
  //       <>
  //         <Row gutter={md ? [12, 16] : [10, 10]}>
  //           {products?.map((product, index) => (
  //             <Col key={index} xs={12} sm={12} md={8} lg={6} xl={6} xxl={4}>
  //               {/* <ProductItem item={{ ...product }} /> */}
  //             </Col>
  //           ))}
  //         </Row>
  //       </>
  //     ),
  //   },
  // ];

  return (
    <>
      <Container
        style={{
          marginTop: md ? 120 : 40,
          position: md ? 'unset' : 'relative',
        }}
      >
        {/* <Banner /> */}
        {md && <BannerMob />}

        {!md && <PromoSection />}
        <CategoriesBySvg listCategory={listCategory} />
        {!md && <BannerMob />}
        {!md && <ProductCards />}
      </Container>
      <Container>
        {/* <TopSection
          wrapperStyles={{
            ...wrapperStyles,
            padding: '10px 20px',
            borderRadius: '25px 8px 8px 8px',
          }}
          leftText={{
            text: '',
          }}
          rightText={{
            text: '',
          }}
        >
          <LatestReveal />
        </TopSection> */}

        {/* <TopSection
          icon={<FireOutlined />}
          wrapperStyles={{
            ...wrapperStyles,
            padding: '0 20px',
            paddingTop: 16,
          }}
          leftText={{
            text: t('home.coming'),
            style: { ...comingSoonStyles },
          }}
          // rightText={{
          //   text: t('home.all'),
          //   style: { cursor: 'pointer', fontSize: 14, color: textColorBase },
          // }}
        >
          <ComingSoon />
        </TopSection> */}

        {/* <TopSection
          icon={<SearchOutlined />}
          wrapperStyles={wrapperStyles}
          leftText={{ text: t('home.trend'), style: trendsStyles }}
        >
          <Trends />
        </TopSection> */}

        <TopSection
          wrapperStyles={{
            ...wrapperStyles,
            background: white,
            padding: 0,
            paddingBottom: 1,
          }}
          leftText={{ text: '' }}
          rightText={{
            text: '',
          }}
        >
          {/* <Tabs type="card" items={tabItems} /> */}
          <Row gutter={md ? [12, 0] : [4, 0]}>
            {listProducts?.map((product, index) => (
              <Col
                key={index}
                xs={12}
                sm={12}
                md={8}
                lg={8}
                xl={4}
                xxl={4}
                style={{ padding: 2 }}
              >
                <ProductItem item={{ ...product }} />
              </Col>
            ))}
          </Row>
          <Button
            onClick={() => {
              startTransition(() => {
                const url = generatePath(ROUTES.PRODUCT_LIST, {
                  categoryId: 'all',
                });

                navigate(url);
              });
            }}
            style={{ display: 'block', margin: '20px auto', minWidth: 150 }}
          >
            {t('home.more')}
          </Button>
        </TopSection>
      </Container>
    </>
  );
};

export default Home;
