import { LatestRevealRes } from '@/dto/product.dto';
import productsService from '@/services/products.service';
import { useCallback, useEffect, useState } from 'react';

export const useLatestReveal = () => {
  const [data, setData] = useState<LatestRevealRes[]>([]);
  const loadData = useCallback(async () => {
    try {
      const res = await productsService.listLatestReveal({
        pageIndex: 1,
        pageSize: 50,
      });
      const list = res?.data || [];
      if (list.length < 6) {
        setData(Array(6).fill(list).flat());
      } else {
        setData(list);
      }
    } catch (error) {}
  }, []);
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
  };
};
