import { ComingSoonRes } from '@/dto/product.dto';
import productsService from '@/services/products.service';
import { useCallback, useEffect, useState } from 'react';

export const useComingSoon = () => {
  const [data, setData] = useState<ComingSoonRes[]>([]);
  const loadData = useCallback(async () => {
    try {
      const res = await productsService.listComingSoon({
        pageIndex: 1,
        pageSize: 50,
      });
      const list = res?.data || [];
      setData(list);
    } catch (error) {}
  }, []);
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
  };
};
