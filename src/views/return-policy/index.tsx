import { FC } from 'react';
import { Link } from 'react-router-dom';
import Container from '@/components/home/<USER>';
import { Text } from '@/components';
import { Grid } from 'antd';
import { useTranslation } from 'react-i18next';

const ReturnPolicy: FC = () => {
  const { t } = useTranslation();
  const ulStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
  };
  const contentStyle: React.CSSProperties = {
    padding: '5px',
  };
  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    textAlign: 'center',
  };
  const { md } = Grid.useBreakpoint();

  return (
    <Container
      style={{
        marginTop: md ? 120 : 40,
        position: md ? 'unset' : 'relative',
        backgroundColor: '#FFFFFF',
      }}
    >
      <h1 style={{ ...titleStyle }}>{t('page.returnPolicy.title')}</h1>
      <div>
        <h4 className="text-lg font-medium">{t('page.returnPolicy.conditions.title')}</h4>
        <ul className="list-disc ml-6 space-y-1">
          <li>{t('page.returnPolicy.conditions.faulty')}</li>
          <li>{t('page.returnPolicy.conditions.original')}</li>
          <li>{t('page.returnPolicy.conditions.video')}</li>
        </ul>
      </div>

      {/* Return Time */}
      <div>
        <h4 className="text-lg font-medium">{t('page.returnPolicy.time.title')}</h4>
        <p className="ml-4">{t('page.returnPolicy.time.content')}</p>
      </div>

      {/* Return Process */}
      <div>
        <h4 className="text-lg font-medium">{t('page.returnPolicy.process.title')}</h4>
        <ul className="list-disc ml-6 space-y-1">
          <li>{t('page.returnPolicy.process.notify')}</li>
          <li>{t('page.returnPolicy.process.info')}</li>
          <li>{t('page.returnPolicy.process.verify')}</li>
          <li>{t('page.returnPolicy.process.resolve')}</li>
        </ul>
      </div>

      {/* Notes */}
      <div>
        <h4 className="text-lg font-medium">{t('page.returnPolicy.note.title')}</h4>
        <ul className="list-disc ml-6 space-y-1">
          <li>{t('page.returnPolicy.note.shipping')}</li>
          <li>{t('page.returnPolicy.note.time')}</li>
          <li>{t('page.returnPolicy.note.refund')}</li>
        </ul>
      </div>

      {/* Video Example */}
      <div>
        <h4 className="text-lg font-medium">{t('page.returnPolicy.videoExample.title')}</h4>
        <p className="whitespace-pre-line ml-4">{t('page.returnPolicy.videoExample')}</p>
      </div>
      {/* <Text style={contentStyle}><b>1. Điều kiện đổi trả:</b></Text> */}
      {/* <Text><b style={{ textDecoration: 'underline' }}>Bước 1:</b> Tìm kiếm sản phẩm</Text> */}
      {/* <ul style={{...ulStyle}}>
        <li>Hàng hóa bị lỗi do nhà sản xuất: Sản phẩm bị lỗi về chất lượng, không hoạt động đúng như mô tả hoặc có lỗi kỹ thuật.</li>
        <li>Hàng hóa còn nguyên vẹn: Sản phẩm phải còn nguyên tem mác, bao bì, phụ kiện đi kèm.</li>
        <li>Có video xác nhận: Khách hàng cần cung cấp video quay rõ ràng sản phẩm bị lỗi để làm bằng chứng. Video phải quay rõ các chi tiết về sản phẩm, lỗi hỏng và ngày quay.</li>
      </ul>
      <Text style={contentStyle}><b>2. Thời gian đổi trả:</b></Text>
      <ul style={{...ulStyle}}>
        <li>Trong vòng 30 ngày kể từ ngày nhận hàng, khách hàng có quyền yêu cầu đổi trả sản phẩm nếu sản phẩm đó bị lỗi do nhà sản xuất.</li>
      </ul>
      <Text style={contentStyle}><b>3. Quy trình đổi trả:</b></Text>
      <ul style={{...ulStyle}}>
        <li>Thông báo: Khách hàng liên hệ với bộ phận chăm sóc khách hàng để thông báo về việc muốn đổi trả sản phẩm.</li>
        <li>Cung cấp thông tin: Khách hàng cung cấp đầy đủ thông tin về đơn hàng, sản phẩm bị lỗi và gửi video xác nhận.</li>
        <li>Kiểm tra: Bộ phận chăm sóc khách hàng sẽ tiến hành kiểm tra thông tin và video mà khách hàng cung cấp.</li>
        <li>Đổi trả: Nếu sản phẩm đáp ứng đủ điều kiện đổi trả, chúng tôi sẽ tiến hành đổi sản phẩm mới tương đương hoặc hoàn tiền cho khách hàng.</li>
      </ul>
      <Text><b style={{ textDecoration: 'underline' }}>Lưu ý:</b></Text>
      <ul style={{...ulStyle}}>
        <li>Phí vận chuyển: Chi phí vận chuyển đổi trả sẽ do Shop Lucky Viet Nam chịu trách nhiệm.</li>
        <li>Thời gian xử lý: Thời gian xử lý yêu cầu đổi trả có thể thay đổi tùy thuộc vào từng trường hợp cụ thể.</li>
        <li>Hình thức hoàn tiền: Chúng tôi sẽ hoàn tiền vào tài khoản ngân hàng hoặc ví điện tử của khách hàng.</li>
      </ul>
      <Text>Ví dụ về nội dung video xác nhận:</Text>
      <Text>Video cần quay rõ các góc cạnh của sản phẩm, tập trung vào phần bị lỗi. Khách hàng nên nói rõ tên sản phẩm, mã đơn hàng và mô tả chi tiết về lỗi hỏng.</Text> */}
    </Container>
  );
};

export default ReturnPolicy;
