import { Tabs, TabsProps } from 'antd';
import { useTranslation } from 'react-i18next';
import './index.css';
import { NSTicket } from '@/enums/NSTicket';
import { ListVoucer } from './ListVoucer';

const TAB_KEY = {
  ALL: 'ALL',
  WINNING: 'WINNING',
  NON_WINNING: 'NON_WINNING',
};

const VoucherInfo = () => {
  const { t } = useTranslation();

  const items: TabsProps['items'] = [
    {
      key: TAB_KEY.ALL,
      label: t('order.all'),
      children: <ListVoucer />,
    },
    {
      key: TAB_KEY.WINNING,
      label: t('order.winning_voucher'),
      children: <ListVoucer status={NSTicket.EStatus.WON} />,
    },
    {
      key: TAB_KEY.NON_WINNING,
      label: t('order.non_winning_voucher'),
      children: <ListVoucer status={NSTicket.EStatus.NOT_WON} />,
    },
  ];
  return (
    <div>
      <Tabs
        defaultActiveKey={TAB_KEY.ALL}
        items={items}
        tabBarGutter={32}
        tabBarStyle={{
          borderBottom: 'none',
          marginBottom: 16,
        }}
        // className="custom-tabs"
        style={{ width: '100%' }}
      />
    </div>
  );
};

export default VoucherInfo;
