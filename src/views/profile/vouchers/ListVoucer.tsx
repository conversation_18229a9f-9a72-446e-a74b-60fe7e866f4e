import { List, Flex, Pagination } from 'antd';
import { useListTickets } from '../hooks/useListTickets';
import { NSTicket } from '@/enums/NSTicket';
import dayjs from 'dayjs';

export const ListVoucer = ({ status }: { status?: NSTicket.EStatus }) => {
  const {
    data: listTickets,
    total,
    currentPage,
    handleChangePage,
  } = useListTickets(status);
  return (
    <div>
      <List
        size="large"
        bordered
        style={{ overflow: 'auto', maxHeight: 450 }}
        dataSource={listTickets}
        renderItem={(item) => (
          <List.Item style={{padding: 5}}>
            <Flex align="center" gap={10} style={{ width: '100%' }}>
              <img src={item.product.cover} width={50} height={50} />
              {item.ticketNumber} - {item.product.name}
            </Flex>
            <div style={{ color: '#aaa', fontSize: 12 }}>{dayjs(item?.purchaseTime).format('DD/MM/YYYY HH:mm')}</div>
          </List.Item>
        )}
      />
      <Flex justify="center" style={{ marginTop: 20 }}>
        <Pagination
          current={currentPage}
          style={{ marginBottom: 20 }}
          pageSize={12}
          total={total}
          onChange={handleChangePage}
        />
      </Flex>
    </div>
  );
};
