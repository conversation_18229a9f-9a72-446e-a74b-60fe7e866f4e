import { Grid, Tabs, TabsProps } from 'antd';
import { useTranslation } from 'react-i18next';
import './index.css';
import { NSOrder } from '@/enums/NSOrder';
import { ListOrder } from './ListOrder';

const ORDER_KEY = {
  ALL: 'ALL',
  PENDING_PAYMENT: NSOrder.EStatus.PENDING_PAYMENT,
  SHIPPING: NSOrder.EStatus.SHIPPING,
  WAITING_FOR_SHIPMENT: NSOrder.EStatus.WAITING_FOR_SHIPMENT,
  COMPLETED: NSOrder.EStatus.COMPLETED,
};

const OrderInfo = () => {
  const { t } = useTranslation();
  const items: TabsProps['items'] = [
    {
      key: ORDER_KEY.ALL,
      label: t('order.all'),
      children: <ListOrder />,
    },
    {
      key: ORDER_KEY.PENDING_PAYMENT,
      label: t('order.pending_payment'),
      children: <ListOrder status={NSOrder.EStatus.PENDING_PAYMENT} />,
    },
    {
      key: ORDER_KEY.WAITING_FOR_SHIPMENT,
      label: t('order.awaiting_shipment'),
      children: <ListOrder status={NSOrder.EStatus.WAITING_FOR_SHIPMENT} />,
    },
    {
      key: ORDER_KEY.SHIPPING,
      label: t('order.order_in_transit'),
      children: <ListOrder status={NSOrder.EStatus.SHIPPING} />,
    },
    {
      key: ORDER_KEY.COMPLETED,
      label: t('order.completed'),
      children: <ListOrder status={NSOrder.EStatus.COMPLETED} />,
    },
  ];
  return (
    <div>
      <Tabs
        defaultActiveKey={ORDER_KEY.ALL}
        items={items}
        tabBarGutter={32}
        tabBarStyle={{
          borderBottom: 'none',
          marginBottom: 16,
        }}
        // className="custom-tabs"
        style={{ width: '100%' }}
      />
    </div>
  );
};

export default OrderInfo;
