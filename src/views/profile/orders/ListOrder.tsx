import {
  List,
  Flex,
  Pagination,
  Tag,
  Image,
  Divider,
  Space,
  theme,
} from 'antd';
import { useListOrder } from '../hooks/useListOrder';
import { NSOrder } from '@/enums/NSOrder';
import { Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { IMyOrder, IMyOrderItem } from '@/dto/order.dto';

const { Text } = Typography;
const OrderStatusColorMap: Record<NSOrder.EStatus, string> = {
  [NSOrder.EStatus.PENDING_PAYMENT]: 'orange',
  [NSOrder.EStatus.PENDING_CONFIRMATION]: 'gold',
  [NSOrder.EStatus.WAITING_FOR_SHIPMENT]: 'blue',
  [NSOrder.EStatus.SHIPPING]: 'geekblue',
  [NSOrder.EStatus.DELIVERED]: 'green',
  [NSOrder.EStatus.COMPLETED]: 'success',
  [NSOrder.EStatus.CANCELED]: 'red',
  [NSOrder.EStatus.RETURN_REQUESTED]: 'volcano',
  [NSOrder.EStatus.RETURNED]: 'magenta',
  [NSOrder.EStatus.REFUNDED]: 'purple',
};

export const ListOrder = ({ status }: { status?: NSOrder.EStatus }) => {
  const { data, total, currentPage, handleChangePage } = useListOrder(status);
  const { t, i18n } = useTranslation();
  const { token } = theme.useToken();
  const renderItem = (order: IMyOrder) => {
    return (
      <List.Item
        style={{
          padding: 16,
          borderRadius: 8,
          border: '1px solid #f0f0f0',
          marginBottom: 12,
        }}
      >
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          {/* Header: Mã đơn + Trạng thái */}
          <Space wrap size="middle">
            <Text strong>Mã đơn:</Text>
            <Text>{order.id}</Text>
            <Tag color={OrderStatusColorMap[order.status]}>
              {t(`order.order_status.${order.status}`)}
            </Tag>
          </Space>

          {/* Ngày & tổng tiền */}
          <Space wrap size="large">
            <Text type="secondary">
              Ngày tạo: {new Date(order.createdDate).toLocaleString()}
            </Text>
            <Text strong>
              <Text style={{ color: '#999' }}>Tổng tiền:</Text>{' '}
              <Text strong style={{ color: '#f53d2d' }}>
                {Number(order.totalAmount).toLocaleString(i18n.language)} VND
              </Text>
            </Text>
          </Space>

          {/* Divider nhỏ */}
          <Divider style={{ margin: '8px 0' }} />

          {/* Danh sách sản phẩm */}
          <List
            size="small"
            dataSource={order.items}
            split={false}
            renderItem={(item: IMyOrderItem) => (
              <List.Item key={item.id} style={{ padding: 0 }}>
                <Space align="start">
                  <Image
                    src={item.productImage}
                    alt={item.productName}
                    width={48}
                    height={48}
                    preview={false}
                    style={{ objectFit: 'cover', borderRadius: 4 }}
                  />
                  <div>
                    <Text strong>{item.productName}</Text>
                    <br />
                    <Text type="secondary">
                      SL: {item.qty} &nbsp;|&nbsp;{' '}
                      <Text>
                        <Text style={{ color: '#999' }}>Giá:</Text>{' '}
                        <Text strong style={{ color: token.colorTextBase }}>
                          {Number(item.price).toLocaleString(i18n.language)} VND
                        </Text>
                      </Text>
                    </Text>
                  </div>
                </Space>
              </List.Item>
            )}
          />
        </Space>
      </List.Item>
    );
  };
  return (
    <div>
      <List
        size="large"
        bordered
        style={{ overflow: 'auto', maxHeight: 450 }}
        dataSource={data}
        renderItem={renderItem}
      />
      <Flex justify="center" style={{ marginTop: 20 }}>
        <Pagination
          current={currentPage}
          style={{ marginBottom: 20 }}
          pageSize={12}
          total={total}
          onChange={handleChangePage}
        />
      </Flex>
    </div>
  );
};
