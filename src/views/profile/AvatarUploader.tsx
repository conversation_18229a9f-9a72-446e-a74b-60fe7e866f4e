import React, { useEffect, useState } from 'react';
import { Upload, Avatar, message, Button, Flex } from 'antd';
import { UploadOutlined, UserOutlined } from '@ant-design/icons';
import type { UploadChangeParam } from 'antd/es/upload';
import { useTranslation } from 'react-i18next';

const AvatarUploader = ({ initialAvatar, onUpload }) => {
  const { t } = useTranslation();
  const [imageUrl, setImageUrl] = useState(initialAvatar);

  const handleChange = (info: UploadChangeParam) => {
    if (info.file.status === 'done' || info.file.status === 'uploading') {
      const file = info.file.originFileObj as File;
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result as string;
        setImageUrl(base64);
        onUpload(file);
      };
      reader.readAsDataURL(file);
    }
  };

  useEffect(() => {
    setImageUrl(initialAvatar);
  }, [initialAvatar]);

  return (
    <Flex vertical align="center" gap={5}>
      <Avatar
        size={128}
        src={imageUrl}
        icon={!imageUrl && <UserOutlined />}
        style={{ marginBottom: 16 }}
      />
      <Upload showUploadList={false} onChange={handleChange}>
        <Button icon={<UploadOutlined />}>{t('auth.upload_avatar')}</Button>
      </Upload>
    </Flex>
  );
};

export default AvatarUploader;
