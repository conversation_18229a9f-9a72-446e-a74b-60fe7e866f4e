import { useCallback, useEffect, useState } from 'react';
import { NSOrder } from '@/enums/NSOrder';
import ordersService from '@/services/orders.service';

export const useListOrder = (status?: NSOrder.EStatus) => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const handleChangePage = (currentPage: number) => {
    setCurrentPage(currentPage);
  };

  const listTickets = useCallback(async () => {
    try {
      const params = {
        pageIndex: currentPage,
        pageSize: pageSize,
      };

      if (status) {
        Object.assign(params, {
          status,
        });
      }
      const res = await ordersService.myOrders(params);
      setData(res?.data || []);
      setTotal(res?.total || 0);
    } catch (error) {}
  }, [currentPage, status]);

  useEffect(() => {
    listTickets();
  }, [listTickets]);

  return {
    data,
    total,
    currentPage,
    pageSize,
    handleChangePage,
  };
};
