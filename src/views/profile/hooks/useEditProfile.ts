import { UpdateProfileReq } from '@/dto/auth-member.dto';
import { toastService } from '@/services/@common';
import authMembersService from '@/services/auth-members.service';
import { Form } from 'antd';
import { useEffect, useState } from 'react';

const initialValue = {
  email: '',
  firstName: '',
  phone: '',
  address: '',
  avatar: '',
};

export const useEditProfile = (initialValue?: UpdateProfileReq) => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [isEdit, setIsEdit] = useState<boolean>();

  useEffect(() => {
    form.setFieldsValue(initialValue);
  }, [initialValue]);

  const onSave = async (values: UpdateProfileReq) => {
    setIsLoading(true);
    try {
      await authMembersService.updateProfile({
        ...values,
      });
      form.resetFields();
      setIsEdit(false);
      window.location.reload();
    } catch (error) {
      toastService.handleError(error);
    }
    setIsLoading(false);
  };

  const handleUploadAvt = async (file: File) => {
    try {
      const res = await authMembersService.uploadAvatar(file);
      console.log('res: ', res);
      await authMembersService.updateProfile({ avatar: res?.fullUrl });
    } catch (error) {
      toastService.handleError(error);
    }
  };

  return {
    isEdit,
    form,
    isLoading,
    setIsEdit,
    onSave,
    handleUploadAvt,
  };
};
