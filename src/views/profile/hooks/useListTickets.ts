import { NSTicket } from '@/enums/NSTicket';
import authMembersService from '@/services/auth-members.service';
import { Form } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';

export const useListTickets = (status?: NSTicket.EStatus) => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const [fromDate, setFromDate] = useState<any>(null);
  const [toDate, setToDate] = useState<any>(null);

  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  const onFinish = async (values: {
    fromDate?: dayjs.Dayjs;
    toDate?: dayjs.Dayjs;
  }) => {
    if (values?.fromDate) {
      setFromDate(values?.fromDate.toISOString());
    } else {
      setFromDate(null);
    }

    if (values?.toDate) {
      setToDate(values?.toDate.toISOString());
    } else {
      setToDate(null);
    }
  };

  const handleChangePage = (currentPage: number) => {
    setCurrentPage(currentPage);
  };

  const listTickets = useCallback(async () => {
    try {
      const params = {
        pageIndex: currentPage,
        pageSize: pageSize,
      };
      if (fromDate) {
        Object.assign(params, {
          fromDate,
        });
      }
      if (toDate) {
        Object.assign(params, {
          toDate,
        });
      }
      if (status) {
        Object.assign(params, {
          status,
        });
      }
      const res = await authMembersService.listTickets(params);
      setData(res?.data || []);
      setTotal(res?.total || 0);
    } catch (error) {}
  }, [currentPage, fromDate, status, toDate]);

  useEffect(() => {
    listTickets();
  }, [listTickets]);

  return {
    data,
    total,
    currentPage,
    pageSize,
    handleChangePage,
    form,
    onFinish,
  };
};
