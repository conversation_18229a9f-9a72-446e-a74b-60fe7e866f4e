import { ChangePasswordReq } from '@/dto/auth-member.dto';
import { toastService } from '@/services/@common';
import authMembersService from '@/services/auth-members.service';
import { Form } from 'antd';
import { useState } from 'react';

export const useChangePassword = () => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  const onFinish = async (values: ChangePasswordReq) => {
    console.log('Received values of form: ', values);
    setIsLoading(true);
    try {
      await authMembersService.changePassword({
        ...values,
      });
      toastService.success('Change password success');
      form.resetFields();
    } catch (error) {
      toastService.handleError(error);
      console.log(`-------------------`);
      console.log({ error });
      console.log(`-------------------`);
    }
    setIsLoading(false);
  };

  return {
    form,
    isLoading,
    onFinish,
  };
};
