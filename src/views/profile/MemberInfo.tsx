import React, {
  CSSProperties,
  use,
  useCallback,
  useEffect,
  useState,
} from 'react';
import {
  Avatar,
  Typography,
  Tag,
  Descriptions,
  Row,
  Col,
  Grid,
  QRCode,
  Space,
  Button,
  Flex,
  Form,
  Input,
  Spin,
} from 'antd';
import { useAuthStore } from '@/stores/authStore';
import { useListTickets } from './hooks/useListTickets';
import { white } from '@/constants/themeColor';
import { useTranslation } from 'react-i18next';
import { MyAddressDto } from '@/dto/auth-member.dto';
import authMembersService from '@/services/auth-members.service';
import { useEditProfile } from './hooks/useEditProfile';

const infoStyle: CSSProperties = {
  backgroundColor: white,
  padding: 20,
};

const LabelValueRow = ({ label, value }) => (
  <Row style={{ marginBottom: 8 }}>
    <Col
      span={8}
      style={{ fontWeight: 500, textAlign: 'left', paddingRight: 8 }}
    >
      {label}:
    </Col>
    <Col span={16}>{value}</Col>
  </Row>
);

const MemberInfo = () => {
  const { member } = useAuthStore();
  const { md } = Grid.useBreakpoint();
  const { t } = useTranslation();
  const [address, setAddress] = useState<MyAddressDto | null>(null);
  const { form, isLoading, isEdit, setIsEdit, onSave } = useEditProfile({
    email: member?.email,
    firstName: member?.firstName,
    phone: member?.phone,
    address: address?.address,
    avatar: member?.avatar,
  });

  const getAddress = useCallback(async () => {
    try {
      const res = (
        await authMembersService.myAddress({ pageIndex: 1, pageSize: 10 })
      ).data.find((item) => item.isDefault);
      setAddress(res);
    } catch (error) {
      setAddress(null);
    }
  }, []);

  const handleEdit = () => {
    setIsEdit(!isEdit);
  };

  useEffect(() => {
    getAddress();
  }, [getAddress]);

  const handleFinish = (values) => {
    onSave(values);
  };

  return (
    <div>
      <h3 style={{ fontWeight: 'bold', color: '#F37421' }}>
        {t('auth.infomation')}
      </h3>
      {isLoading ? (
        <Spin size="small" />
      ) : (
        <div>
          <Form form={form} layout="vertical" onFinish={handleFinish}>
            <LabelValueRow
              label={t('auth.username')}
              value={member?.username}
            />
            <LabelValueRow
              label={t('auth.email')}
              value={
                isEdit ? (
                  <Form.Item name="email" style={{ marginBottom: 0 }}>
                    <Input />
                  </Form.Item>
                ) : (
                  member?.email
                )
              }
            />
            <LabelValueRow
              label={t('auth.firstName')}
              value={
                isEdit ? (
                  <Form.Item name="firstName" style={{ marginBottom: 0 }}>
                    <Input />
                  </Form.Item>
                ) : (
                  member?.firstName
                )
              }
            />
            <LabelValueRow
              label={t('auth.phone')}
              value={
                isEdit ? (
                  <Form.Item name="phone" style={{ marginBottom: 0 }}>
                    <Input />
                  </Form.Item>
                ) : (
                  member?.phone
                )
              }
            />
            <LabelValueRow
              label={t('auth.address')}
              value={
                isEdit ? (
                  <Form.Item name="address" style={{ marginBottom: 0 }}>
                    <Input />
                  </Form.Item>
                ) : (
                  address?.address
                )
              }
            />
            <Flex align="center" gap={10} style={{ marginTop: 20 }}>
              {isEdit ? (
                <Button
                  type="primary"
                  block
                  style={{
                    backgroundColor: '#ffffff',
                    width: 100,
                    color: '#000000',
                    border: '1px solid #F37421',
                  }}
                  onClick={handleEdit}
                >
                  {t('auth.cancel')}
                </Button>
              ) : (
                <Button
                  type="primary"
                  block
                  style={{ backgroundColor: '#F37421', width: 100 }}
                  onClick={handleEdit}
                >
                  {t('auth.edit')}
                </Button>
              )}
              {isEdit && (
                <Form.Item style={{ marginBottom: 0 }}>
                  <Button
                    type="primary"
                    block
                    style={{ backgroundColor: '#F37421', width: 100 }}
                    htmlType="submit"
                  >
                    {t('auth.save')}
                  </Button>
                </Form.Item>
              )}
            </Flex>
          </Form>
        </div>
      )}
    </div>
  );
};

export default MemberInfo;
