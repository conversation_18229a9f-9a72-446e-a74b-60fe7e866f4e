import { Col, Row, Button, Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';
import { white } from '@/constants/themeColor';
import { useChangePassword } from './hooks/useChangePassword';

const ChangePassword = () => {
  const { t } = useTranslation();
  const { form, isLoading, onFinish } = useChangePassword();

  return (
    <Row gutter={[16, 16]} style={{ padding: 20, backgroundColor: white }}>
      <Col xs={24} md={24}>
        <h2>{t('profile.change_password.title')}</h2>
        <Form
          name="changePassword"
          form={form}
          onFinish={onFinish}
          style={{
            width: '50%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          {/* Old Password Input */}
          <Form.Item
            name="oldPassword"
            style={{ width: '100%', marginBottom: '16px' }}
            rules={[
              {
                required: true,
                message: t('profile.change_password.old_password_required'),
              },
            ]}
          >
            <Input.Password
              placeholder={t('profile.change_password.old_password')}
              style={{ width: '100%', padding: '10px' }}
            />
          </Form.Item>

          {/* New password Input */}
          <Form.Item
            name="newPassword"
            style={{ width: '100%', marginBottom: '16px' }}
            rules={[
              {
                required: true,
                message: t('profile.change_password.new_password_required'),
              },
            ]}
          >
            <Input.Password
              placeholder={t('profile.change_password.new_password')}
              style={{ width: '100%', padding: '10px' }}
            />
          </Form.Item>

          {/* Change password Button */}
          <Form.Item style={{ width: '100%', marginBottom: '16px' }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ width: '100%', backgroundColor: '#3E97FF' }}
            >
              {t('profile.change_password.btn_update')}
            </Button>
          </Form.Item>
        </Form>
      </Col>
    </Row>
  );
};

export default ChangePassword;
