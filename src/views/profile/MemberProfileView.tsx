import React, { Children, useEffect, useState } from 'react';
import {
  Avatar,
  Typography,
  Tabs,
  Row,
  Col,
  Grid,
  Card,
  Tag,
  Descriptions,
  Space,
  QRCode,
  List,
} from 'antd';
import {
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
  WalletOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '@/stores/authStore';
import Container from '@/components/home/<USER>';
import MemberInfo from './MemberInfo';
import { useTranslation } from 'react-i18next';
import NotificationsPage from '../notification';
import ListTicket from './ListTicket';
import { useNavigate, useParams } from 'react-router-dom';
import ChangePassword from './ChangePassword';
import WalletBalance from './WalletBalance';
import OrderInfo from './orders/OrderInfo';
import VoucherInfo from './vouchers/VoucherInfo';
import { useListTickets } from './hooks/useListTickets';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import AvatarUploader from './AvatarUploader';
import { useEditProfile } from './hooks/useEditProfile';

const { Title, Text } = Typography;

const MemberProfileView = () => {
  const { t } = useTranslation();
  const { logout, member } = useAuthStore();
  const { startTransition } = useGlobalContext();
  const navigate = useNavigate();
  const { md } = Grid.useBreakpoint();
  const { tab } = useParams<{ tab: string }>();
  const [activeTab, setActiveTab] = useState('1');
  const { data: listTickets } = useListTickets();

  const { handleUploadAvt } = useEditProfile();

  useEffect(() => {
    setActiveTab(tab);
  }, [tab]);

  const handleChangeTab = (e) => {
    setActiveTab(e);
  };

  const onLogout = () => {
    startTransition(() => {
      logout();
      navigate('/');
    });
  };

  const tabItem = [
    { label: t('auth.general'), key: '1', children: <MemberInfo /> },
    {
      label: t('auth.notification'),
      key: '2',
      children: <NotificationsPage />,
    },
    // {
    //   label: t('ticket.title'),
    //   key: '3',
    //   children: <ListTicket />,
    // },
    // {
    //   label: t('profile.change_password.title'),
    //   key: '3',
    //   children: <ChangePassword />,
    // },
    {
      label: t('profile.wallet_balance'),
      key: '4',
      children: <WalletBalance />,
    },
    {
      label: t('profile.order_information'),
      key: '5',
      children: <OrderInfo />,
    },
    {
      label: t('profile.voucher_information'),
      key: '6',
      children: <VoucherInfo />,
    },
  ];

  return (
    <Container
      style={{
        marginTop: md ? 120 : 60,
        position: md ? 'unset' : 'relative',
        backgroundColor: '#FFFFFF',
      }}
    >
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div
            style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}
          >
            <AvatarUploader
              initialAvatar={member?.avatar}
              onUpload={handleUploadAvt}
            />
            <div style={{ marginLeft: 20 }}>
              <Title level={3} style={{ color: '#F37421' }}>
                {t('auth.hello')} {member?.firstName}
              </Title>
              <a onClick={onLogout}>{t('auth.logout')}</a>
            </div>
          </div>
          <Tabs
            tabPosition={md ? 'left' : 'top'}
            tabBarStyle={{ border: 'none' }}
            activeKey={activeTab}
            style={{ border: 'none' }}
            items={tabItem}
            onChange={handleChangeTab}
          ></Tabs>
        </Col>
      </Row>
    </Container>
  );
};

export default MemberProfileView;
