import {
  Col,
  Flex,
  List,
  Pagination,
  Row,
  Typo<PERSON>,
  Button,
  Form,
  Input,
  DatePicker,
  Grid,
} from 'antd';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { white } from '@/constants/themeColor';
import { useListTickets } from './hooks/useListTickets';
import { SearchOutlined } from '@ant-design/icons';

const ListTicket = () => {
  const { md } = Grid.useBreakpoint();
  const { t } = useTranslation();
  const {
    data: listTickets,
    total,
    currentPage,
    pageSize,
    handleChangePage,
    form,
    onFinish,
  } = useListTickets();

  return (
    <Row gutter={[16, 16]} style={{ backgroundColor: !md ? 'white' : 'transparent' }}>
      <Col xs={24} md={24}>
        <Row>
          <Col xs={24} md={8}>
            <h2 style={{ color: '#F37421' }}>{t('ticket.title')}</h2>
          </Col>
          <Col xs={24} md={16}>
            <Form
              name="searchTicket"
              form={form}
              onFinish={onFinish}
              style={{
                width: '100%',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Row
                gutter={[16, 4]}
                justify={'end'}
                style={{
                  flex: 1,
                }}
              >
                <Col>
                  <Form.Item name="fromDate" style={{ width: '100%' }}>
                    <DatePicker
                      picker="date"
                      placeholder={t('profile.list_ticket.from_date')}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col>
                  <Form.Item name="toDate" style={{ width: '100%' }}>
                    <DatePicker
                      placeholder={t('profile.list_ticket.to_date')}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col>
                  <Form.Item style={{ width: '100%', marginBottom: '16px' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      style={{ width: '100%', backgroundColor: '#3E97FF' }}
                      icon={<SearchOutlined />}
                    >
                      {t('common.label.search')}
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Col>
        </Row>
        <List
          itemLayout="horizontal"
          dataSource={listTickets}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <div
                    style={{
                      width: 50,
                      height: 50,
                      backgroundColor: '#f0f0f0',
                      borderRadius: '50%',
                    }}
                  >
                    <img src={item?.product?.cover} width={50} height={50} />
                  </div>
                }
                title={
                  <span style={{ fontWeight: item.isRead ? 'normal' : 'bold' }}>
                    {item?.product?.name}
                  </span>
                }
                description={
                  <Flex vertical>
                    <Typography.Text>
                      {t('ticket.ticket_code', { code: item?.ticketNumber })}
                    </Typography.Text>
                    <Typography.Text style={{ color: '#aaa' }}>
                      {dayjs(item.createdDate).format('DD/MM/YYYY')}
                    </Typography.Text>
                  </Flex>
                }
              />
            </List.Item>
          )}
        />
        <Pagination
          style={{ textAlign: 'center', marginTop: 20 }}
          current={currentPage}
          pageSize={pageSize}
          total={total}
          onChange={(page) => handleChangePage(page)}
        />
      </Col>
    </Row>
  );
};

export default ListTicket;
