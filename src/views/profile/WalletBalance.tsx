import React, { CSSProperties, useCallback, useEffect, useState } from 'react';
import {
  Typography,
  Descriptions,
  Row,
  Col,
  Grid,
  Tag,
  Space,
  QRCode,
  Input,
  Button,
} from 'antd';
import { useAuthStore } from '@/stores/authStore';
import { useTranslation } from 'react-i18next';
import { MyAddressDto } from '@/dto/auth-member.dto';
import authMembersService from '@/services/auth-members.service';
import ordersService from '@/services/orders.service';
import { VnpayCreatePaymentReq } from '@/dto/order.dto';
import { NSPayment } from '@/enums/NSPayment';
import { toastService } from '@/services/@common';

const { Title, Text } = Typography;

const WalletBalance = () => {
  const { member } = useAuthStore();
  const { md } = Grid.useBreakpoint();
  const { t } = useTranslation();
  const [address, setAddress] = useState<MyAddressDto | null>(null);
  const [qtyCoin, setQtyCoin] = useState('');
  const getAddress = useCallback(async () => {
    try {
      const res = (
        await authMembersService.myAddress({ pageIndex: 1, pageSize: 10 })
      ).data.find((item) => item.isDefault);
      setAddress(res);
    } catch (error) {
      setAddress(null);
    }
  }, []);

  useEffect(() => {
    getAddress();
  }, [getAddress]);

  const deposit = async () => {
    try {
      const amount = Number(qtyCoin) * 1000;
      const req: VnpayCreatePaymentReq = {
        amount,
        purpose: NSPayment.EPaymentPurpose.DEPOSIT,
        // bankCode: 'VNPAYQR',
        returnUrl: `${window.location.origin}/payment-success`,
      };
      const res = await ordersService.vnpayCreatePayment(req);
      if (res?.paymentUrl) {
        window.location.href = res.paymentUrl;
      }
    } catch (error) {
      toastService.handleError(error);
    }
  };
  return (
    <div>
      <Row
        gutter={[16, 16]}
        style={{ backgroundColor: !md ? 'white' : 'transparent' }}
      >
        <Col xs={24} md={18}>
          <h3 style={{ fontWeight: 'bold', color: '#F37421' }}>{t('auth.balance')}</h3>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Descriptions
                size="middle"
                column={1}
                labelStyle={{ color: 'black' }}
              >
                <Descriptions.Item label={t('auth.balance')}>
                  {member?.balance}{' '}
                  <Text style={{ color: '#F37421', marginLeft: 5 }}>
                    {t('common.payment_currency')}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label={t('profile.deposit')}>
                  <Input
                    type="number"
                    value={qtyCoin}
                    onChange={(e) => setQtyCoin(e.target.value)}
                  />
                  <Button
                    type="primary"
                    block
                    style={{ backgroundColor: '#F37421', margin: '0 10px' }}
                    onClick={deposit}
                  >
                    {t('common.deposit_coin')}
                  </Button>
                </Descriptions.Item>
                {/* <Descriptions.Item label="QR Code (Deposit USDT to TRON chain)">
                  <QRCode value={member?.walletAddress} />
                </Descriptions.Item> */}
              </Descriptions>
              <div>
                {/* <div> {QR Code (Deposit USDT to TRON chain)}: </div>{' '} */}
                <div> {t('profile.deposit_tron')}: </div>{' '}
                <QRCode value={member?.walletAddress} />
              </div>
            </Col>
          </Row>
        </Col>
      </Row>
    </div>
  );
};

export default WalletBalance;
