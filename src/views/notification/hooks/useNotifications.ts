import { ListNotificationsDto } from '@/dto/auth-member.dto';
import authMembersService from '@/services/auth-members.service';
import { useCallback, useEffect, useState } from 'react';

export const useNotifications = () => {
  const [data, setData] = useState<ListNotificationsDto[]>();
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  const listNotifications = useCallback(async () => {
    try {
      const res = await authMembersService.listNotification({
        pageIndex: currentPage,
        pageSize: 1000,
      });
      setData(res?.data);
      setTotal(res?.total);
    } catch (error) {}
  }, [currentPage]);

  const handleMarkAsRead = async (id: string[]) => {
    try {
      await authMembersService.markAsRead(id);
      listNotifications();
    } catch (error) {}
  };

  useEffect(() => {
    listNotifications();
  }, [listNotifications]);

  const handleChangePage = (currentPage: number) => {
    setCurrentPage(currentPage);
  };
  return { data, total, currentPage, handleChangePage, handleMarkAsRead };
};
