import { Button, Col, Flex, Grid, List, Pagination, Row } from 'antd';
import { useNotifications } from './hooks/useNotifications';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { Icons } from '@/components';

const NotificationsPage = () => {
  const { t, i18n } = useTranslation();

  const { md } = Grid.useBreakpoint();
  const {
    data: listNotifications,
    total,
    currentPage,
    handleChangePage,
    handleMarkAsRead,
  } = useNotifications();
  const pageSize = 10;
  const bellYellowImg = '/images/bell.png';

  const handleMarkAllAsRead = () => {
    const ids = listNotifications?.filter((n) => !n.isRead).map((n) => n.id);
    handleMarkAsRead(ids);
  };

  return (
    <Row
      gutter={[16, 16]}
      style={{ backgroundColor: !md ? 'white' : 'transparent' }}
    >
      <Col xs={24} md={24}>
        <Flex gap={10} justify="space-between" align="center">
          <h3 style={{ fontWeight: 'bold', color: '#F37421' }}>
            {t('auth.notification')}
          </h3>
          <Button
            icon={<Icons src="read-all-icon" size={20} />}
            onClick={handleMarkAllAsRead}
          />
        </Flex>
        <List
          style={{
            maxHeight: 600,
            overflowY: 'auto',
          }}
          itemLayout="horizontal"
          dataSource={listNotifications}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                style={{ alignItems: 'center' }}
                avatar={
                  <img
                    src={bellYellowImg}
                    width={40}
                    height={40}
                    alt="Bell Icon"
                  />
                }
                title={
                  <div style={{ display: 'flex', flexDirection: 'column' }}>
                    <span
                      style={{
                        fontWeight: item.isRead ? 'normal' : 'bold',
                        fontSize: 16,
                        color: '#f35245',
                      }}
                    >
                      {i18n.language === 'vi'
                        ? item.titleVi
                        : i18n.language === 'en'
                          ? item.title
                          : item.titleCn}
                    </span>
                    <span
                      style={{
                        color: '#aaa',
                        fontSize: 12,
                        fontWeight: 'normal',
                      }}
                    >
                      {dayjs(item.createdDate).format('DD/MM/YYYY HH:mm')}
                    </span>
                  </div>
                }
                description={
                  <div>
                    {i18n.language === 'vi'
                      ? item.bodyVi
                      : i18n.language === 'en'
                        ? item.body
                        : item.bodyCn}
                  </div>
                }
              />
            </List.Item>
          )}
        />
        <Pagination
          style={{ textAlign: 'center', marginTop: 20 }}
          current={currentPage}
          pageSize={pageSize}
          total={total}
          onChange={(page) => handleChangePage(page)}
        />
      </Col>
    </Row>
  );
};

export default NotificationsPage;
