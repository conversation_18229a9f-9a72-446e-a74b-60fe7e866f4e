import { FC } from 'react';
import { Link } from 'react-router-dom';
import Container from '@/components/home/<USER>';
import { Text } from '@/components';
import { Grid } from 'antd';

const Contact: FC = () => {
  const ulStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '10px'
  };
  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    textAlign: 'center'
  };

  const {md} = Grid.useBreakpoint();
  return (
    <Container style={{ marginTop: md ? 120 : 40,  position: md ? 'unset': 'relative', backgroundColor: '#FFFFFF' }}>
      <h1 style={{...titleStyle}}>Liên hệ</h1>
      <ul style={{...ulStyle}}>
        <li>Điện thoại: 0909 689 026</li>
        <li>Địa chỉ web: shoplucky.vn</li>
        <li>Facebook: Shop Lucky Viet Nam</li>
        <li>Tiktok: shoplucky88</li>
      </ul>
    </Container>
  );
};

export default Contact;
