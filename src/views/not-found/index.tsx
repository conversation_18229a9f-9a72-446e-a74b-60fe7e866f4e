import { FC } from 'react';
import { Link } from 'react-router-dom';
import Container from '@/components/home/<USER>';
import { Grid } from 'antd';

const NotFoundPage: FC = () => {
  const {md} = Grid.useBreakpoint();
  return (
    <Container style={{ marginTop: md ? 120 : 40,  position: md ? 'unset': 'relative', backgroundColor: '#FFFFFF'  }}>
      <h1>404 - Page Not Found</h1>
      <p>Sorry, the page you are looking for does not exist.</p>
      <Link to="/">Go to Homepage</Link>
    </Container>
  );
};

export default NotFoundPage;
