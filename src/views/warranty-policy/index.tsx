import { FC, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import Container from '@/components/home/<USER>';
import { Text } from '@/components';
import { Grid } from 'antd';
import { useTranslation } from 'react-i18next';

const contentVi = `<h1 style="font-size: 24px; text-align: center;">CHÍNH SÁCH BẢO HÀNH SẢN PHẨM</h1><div>Bảo hành sản phẩm là khắc phục những lỗi hỏng hóc, sự cố kỹ thuật xảy ra do lỗi của nhà sản xuất.</div><div><b style="text-decoration: underline;">Điều kiện về bảo hành:</b></div><div>Sản phẩm được bảo hành miễn phí nếu sản phẩm đó đáp ứng đủ các điều kiện sau:</div><ul style="display: flex; flex-direction: column; gap: 10px;"><li><PERSON><PERSON>n thời hạn bảo hành (đư<PERSON><PERSON> tính kể từ ngày khách hàng nhận được sản phẩm)</li><li>Khách hàng có đủ cả hóa đơn bán hàng của SHOP LUCKY VIET NAM, phiếu bảo hành, tem bảo hành theo quy định.</li></ul><div><b style="text-decoration: underline;">Nơi nhận bảo hành:</b></div><div>Shop Lucky Viet Nam nhận sản phẩm cần bảo hành của khách: Khách hàng phản ánh sản phẩm cần bảo hành (nếu có thể) đến hệ thống của Shop Lucky Viet Nam. Chúng tôi sẽ có trách nhiệm kiểm tra, sửa chữa lại sản phẩm. Sau khi sản phẩm được bảo hành, Shop Lucky Viet Nam sẽ thông báo cho khách hàng trên website <a href="https://shoplucky.vn">https://shoplucky.vn</a> hoặc thông báo qua các phương thức liên lạc đã trao đổi trước đấy.</div><div>Những trường hợp không được bảo hành.</div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>Sản phẩm đã hết thời hạn bảo hành.</li><li>Phiếu bảo hành, Tem bảo hành bị mất; Tem bảo hành bị dán đè, hoặc Tem bảo hành bị sửa đổi nội dung (kể cả Tem bảo hành gốc).</li><li>Phiếu bảo hành không được điền đầy đủ các thông tin khách hàng và các thông tin trên sản phẩm không trùng khớp với thông tin ghi trên phiếu bảo hành.</li><li>Hóa đơn bán hàng bị mất không đọc được thông tin về sản phẩm.</li></ul>`;
const contentEn = `<h1 style="font-size: 24px; text-align: center;">PRODUCT WARRANTY POLICY</h1><div>Product warranty covers repairs of defects or technical issues caused by manufacturing faults.</div><div><b style="text-decoration: underline;">Warranty Conditions:</b></div><div>Products are eligible for free warranty if they meet the following conditions:
</div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>Still within the warranty period (calculated from the date the customer receives the product)</li><li>Customer must have the original invoice from SHOP LUCKY VIET NAM, the warranty card, and the warranty seal as required.
</li></ul><div><b style="text-decoration: underline;">Warranty Service Location:
</b></div><div>Shop Lucky Viet Nam will receive the product from the customer: Customers can report the issue (if possible) to the Shop Lucky Viet Nam system. We are responsible for checking and repairing the product. After servicing, Shop Lucky Viet Nam will notify the customer via https://shoplucky.vn or other agreed contact methods.</div><div><b style="text-decoration: underline;">Non-warranty Cases:</b></div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>Products that are beyond the warranty period.
</li><li>Lost or tampered warranty cards/seals; warranty seals that are overlapped or altered in content (including original seals).
</li><li>Warranty cards not fully completed with customer information or with mismatched details compared to the product.
</li><li>The sales invoice is lost and the product information cannot be read.</li></ul>`;
const contentCn = `<h1 style="font-size: 24px; text-align: center;">产品保修政策</h1><div> 产品保修是指修复因制造商故障导致的损坏或技术问题。</div><div><b style="text-decoration: underline;">保修条件：</b></div><div>如果产品符合以下条件，则可享受免费保修：</div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>仍在保修期内（从客户收到产品之日算起）</li><li>客户需提供SHOP LUCKY VIET NAM的发票、保修卡和符合规定的保修标签。</li></ul><div><b style="text-decoration: underline;">保修接收地点：</b></div><div>Shop Lucky Viet Nam 将接收客户需保修的产品：客户可将需保修的产品（如可行）反馈给Shop Lucky Viet Nam系统。我们将负责检查并修复产品。保修完成后，Shop Lucky Viet Nam将通过 https://shoplucky.vn 或之前沟通的联系方式通知客户。</div><div><b style="text-decoration: underline;">以下情况不予保修：</b></div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>产品已超出保修期。</li><li>保修卡或保修标签丢失；保修标签被覆盖、篡改（包括原始标签）。</li><li> 保修卡未填写完整的客户信息，或产品信息与保修卡信息不符。</li><li>销售发票丢失，无法读取产品信息。</li></ul>`;

const WarrantyPolicy: FC = () => {
  const [content, setContent] = useState(contentVi);
  const { i18n } = useTranslation();
  const { md } = Grid.useBreakpoint();

  useEffect(() => {
    if (i18n.language === 'vi') {
      setContent(contentVi);
    } else if (i18n.language === 'en') {
      setContent(contentEn);
    } else if (i18n.language === 'cn') {
      setContent(contentCn);
    }
  }, [i18n.language]);

  return (
    <Container
      style={{
        marginTop: md ? 120 : 40,
        position: md ? 'unset' : 'relative',
        backgroundColor: '#FFFFFF',
      }}
    >
      <div dangerouslySetInnerHTML={{ __html: content }}></div>
    </Container>
  );
};

export default WarrantyPolicy;
