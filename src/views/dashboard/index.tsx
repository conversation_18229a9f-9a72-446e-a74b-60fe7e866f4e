import { Foot<PERSON>, Header } from '@/components';
import { ROUTES } from '@/enums/Routes';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { Grid, Layout } from 'antd';
import { Suspense, useEffect, useMemo } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Loader from '../loader';

// TODO: update by constants
const withOutPadding: string[] = [ROUTES.HOME, ROUTES.PRODUCT_LIST];

const RootPage = () => {
  const location = useLocation();
  const { isPending } = useGlobalContext();
  const { xl, md } = Grid.useBreakpoint();

  // const padding = useMemo(() => {
  //   if (withOutPadding.includes(location.pathname)) return 0;
  //   if (!md) return '10px 0';
  //   return '10px 8vw';
  // }, [location.pathname]);

  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, [location.pathname]);

  return (
    <Layout
      style={{
        height: '100%',
        minHeight: '100vh',
      }}
    >
      {/* Header */}
      {/* <div style={{ marginBottom: xl ? 90 : 84 }}> */}
      {/* <Layout.Content> */}
      <Header />
      {/* </Layout.Content> */}
      {/* </div> */}
      {/* Body */}
      {useMemo(
        () => (
          <Layout.Content style={{ flexGrow: 1, zIndex: 9 }}>
            {isPending ? (
              <Loader />
            ) : (
              <Suspense fallback={<Loader />}>
                <Outlet />
              </Suspense>
            )}
          </Layout.Content>
        ),
        [isPending]
      )}
      <Footer />
    </Layout>
  );
};

export default RootPage;
