import { FC } from 'react';
import { Link } from 'react-router-dom';
import Container from '@/components/home/<USER>';
import { Text } from '@/components';
import { Grid } from 'antd';

const HappyShop: FC = () => {
  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    textAlign: 'center'
  };
  const {md} = Grid.useBreakpoint();

  return (
    <Container style={{ marginTop: md ? 120 : 40,  position: md ? 'unset': 'relative', backgroundColor: '#FFFFFF'  }}>
      <h1 style={{...titleStyle}}>CÔNG TY SHOP LUCKY VIET NAM - là chủ sở hữu website trực tiếp bán hàng hóa.</h1>
      <Text>Các nhà cung cấp khác không được tham gia cung cấp dịch vụ trên website.</Text>
      <Text>Hình thức/cách thức tham gia: Thông qua việc ký kết hợp đồng bán buôn/bán lẻ với các <PERSON> cung cấp hàng hóa, <a href="https://shoplucky.vn">https://shoplucky.vn</a> trực tiếp đến lấy hàng ==&gt; vận chuyển đến tận kho của mình và chuyển tới người mua.</Text>
      <Text><a href="https://shoplucky.vn">https://shoplucky.vn</a> trực tiếp thu giá hàng hóa từ phía người mua/sử dụng dịch vụ và chịu trách nhiệm xuất hóa đơn cho người mua/sử dụng dịch vụ.</Text>
      <Text>Nhà cung cấp hàng hóa không được tham gia vào việc định giá hay làm việc, thu phí từ phía người sử dụng, không tham gia trực tiếp đăng tải hay tạo gian hàng trên hệ thống. Các sản phẩm hoàn toàn do <a href="https://shoplucky.vn">https://shoplucky.vn</a> đăng tải do hàng đã nhập về kho.</Text>
      <Text><b style={{ textDecoration: 'underline' }}>Biểu phí</b></Text>
      <Text>Biểu phí của từng mặt hàng sẽ được công khai trên website, người mua có thể trực tiếp nhìn thấy giá sản phẩm và quyết định “Có” hay không đồng ý mua và cho vào đơn hàng.</Text>
    </Container>
  );
};

export default HappyShop;
