import { FC, useEffect, useState } from 'react';
import Container from '@/components/home/<USER>';
import { useTranslation } from 'react-i18next';

const contentVi = `<h1 style="font-size: 24px; text-align: center;">GIỚI THIỆU</h1><div>Chào mừng quý khách hàng đến “Shop Lucky Viet Nam”. Chúng tôi mang đến một trải nghiệm mua sắm hoàn toàn mới lạ đáng để trải nghiệm, nơi khách hàng là trung tâm.</div><div>Bạn yêu thích hàng hiệu nhưng ngân sách có hạn? Đừng lo, Lucky Shop Vietnam sẽ giúp bạn hiện thực hóa giấc mơ sở hữu những món đồ chất lượng cao với giá cực kỳ hấp dẫn!</div><div>Hàng triệu sản phẩm từ các thương hiệu nổi tiếng và uy tín đang chờ bạn khám phá. <PERSON><PERSON> thời trang, phụ kiện hàng hiệu, đồ gia dụng đến điện tử, tất cả đều có tại Shop Lucky Viet Nam.</div><div>Tham gia cộng đồng người dùng của chúng tôi để cập nhật những xu hướng mới nhất, chia sẻ kinh nghiệm mua sắm và nhận được sự hỗ trợ nhiệt tình.</div><div>Đội ngũ hỗ trợ khách hàng của chúng tôi luôn sẵn sàng phục vụ bạn 24/7. Mọi thắc mắc của bạn sẽ được giải đáp nhanh chóng và tận tình.</div><div>Sứ mệnh: 'Mang đến trải nghiệm mua sắm trực tuyến đơn giản, nhanh chóng và thú vị nhất cho mọi khách hàng. Mang đến những sản phẩm chất lượng cao giá hấp dẫn, đáp ứng mọi nhu cầu của khách hàng.'</div><div>Tầm nhìn: Chúng tôi không ngừng nỗ lực để trở thành sàn thương mại điện tử số 1 Việt Nam, mang đến những trải nghiệm mua sắm tốt nhất cho khách hàng.</div><div>Chất lượng: Cung cấp những sản phẩm chất lượng cao, giá trị lớn được tuyển chọn kỹ lưỡng từ các sàn thương mại điện tử hàng đầu và các nhà cung cấp đáng tin cậy. Chúng tôi đảm bảo chất lượng sản phẩm và nguồn gốc xuất xứ rõ ràng.</div><div>Lời mời trải nghiệm: Hãy cùng chúng tôi khám phá một thế giới mua sắm mới. Truy cập web ngay hôm nay và tận hưởng những ưu đãi hấp dẫn nhé!</div>`;
const contentEn = `<h1 style="font-size: 24px; text-align: center;">ABOUT US</h1>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Welcome to “Shop Lucky Vietnam”. We bring you a completely new and unique shopping experience, where customers are at the center.</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Love brand-name goods but have a limited budget? Don’t worry, Lucky Shop Vietnam will help you realize your dream of owning high-quality products at extremely attractive prices!</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Millions of products from famous and reputable brands are waiting for you to explore. From fashion, brand-name accessories, home appliances to electronics, everything is available at Shop Lucky Vietnam.</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Join our user community to stay updated on the latest trends, share shopping experiences, and receive enthusiastic support.</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Our customer support team is always ready to serve you 24/7. All your inquiries will be answered promptly and attentively.</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Mission: “To provide the simplest, fastest, and most enjoyable online shopping experience for every customer. To offer high-quality products at attractive prices, meeting all customer needs.”</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Vision: We constantly strive to become Vietnam’s number one e-commerce platform, delivering the best shopping experiences for customers.</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Quality: Providing high-quality, great-value products carefully selected from leading e-commerce platforms and trusted suppliers. We guarantee product quality and clear origin.</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Invitation to experience: Let’s explore a new world of shopping together. Visit our website today and enjoy great deals!</span></span></p>`;
const contentCn = `<h1 style="font-size: 24px; text-align: center;">介绍</h1>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">欢迎光临“Shop Lucky 越南”。我们为您带来全新的购物体验，在这里客户始终是中心。</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">您喜欢品牌商品但预算有限？别担心，Lucky Shop Vietnam 将帮助您实现拥有高品质商品的梦想，价格非常诱人！</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">数百万来自知名品牌和信誉良好的产品正等待您来发现。从时尚、品牌配饰、家用电器到电子产品，Shop Lucky 越南一应俱全。</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">加入我们的用户社区，了解最新趋势，分享购物经验，并获得热情支持。</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">我们的客户服务团队全天候为您服务。您的所有疑问都会迅速且贴心地得到解答。</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">使命：“为每一位客户提供最简单、最快捷、最有趣的在线购物体验。提供高质量、价格诱人的产品，满足客户的所有需求。”</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">愿景：我们不断努力，致力于成为越南第一的电子商务平台，为客户带来最佳购物体验。</span></span></p>
<p style="line-height:1.3800000000000001;margin-bottom:12pt;margin-top:12pt;" dir="ltr"><span style="background-color:transparent;color:#000000;font-size:11pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">质量：精心挑选来自顶级电商平台和可信供应商的高品质、高价值产品。我们保证产品质量和清晰的来源。</span></span></p>
<p><span style="background-color:transparent;color:#000000;font-size:12pt;"><span style="font-style:normal;font-variant:normal;font-weight:400;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">体验邀请：快来与我们一起探索全新的购物世界。立即访问我们的网站，享受诱人优惠吧！</span></span><br>&nbsp;</p>`;

const AboutUs: FC = () => {
  const [content, setContent] = useState(contentVi);
  const { i18n } = useTranslation();

  useEffect(() => {
    if (i18n.language === 'vi') {
      setContent(contentVi);
    } else if (i18n.language === 'en') {
      setContent(contentEn);
    } else if (i18n.language === 'cn') {
      setContent(contentCn);
    }
  }, [i18n.language]);

  return (
    <Container style={{ marginTop: 100, backgroundColor: '#FFFFFF' }}>
      <div dangerouslySetInnerHTML={{ __html: content }}></div>
    </Container>
  );
};

export default AboutUs;
