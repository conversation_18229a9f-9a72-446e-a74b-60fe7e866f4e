import { FC, useEffect, useState } from 'react';
import Container from '@/components/home/<USER>';
import { Grid } from 'antd';
import { useTranslation } from 'react-i18next';

const contentVi = `<h1 style="font-size: 24px; text-align: center;">CHÍNH SÁCH BẢO MẬT THANH TOÁN</h1><div>Quý khách hàng có thể thanh toán bằng 2 hình thức thanh toán sau:</div><div style="padding: 5px;"><b>1. <PERSON><PERSON> toán tiền khi mua hàng trực tiếp hoặc nhận hàng qua đơn vị vận chuyển.</b></div><div style="padding: 5px;"><b>2. Thanh toán chuyển khoản (liên hệ với Shop Lucky Viet Nam để được hướng dẫn chi tiết):</b></div><ul style="display: flex; flex-direction: column; gap: 10px;"><li><PERSON><PERSON> tài khoản</li><li><PERSON>ố tài khoản – <PERSON><PERSON> hàng</li><li>Nội dung: Số điện thoại mua hàng + Mã đơn hàng</li></ul><div><b style="text-decoration: underline;">Lưu ý:</b> Quý khách hàng nên liên hệ với Shop Lucky Viet Nam theo hotline 0909 689 026 để được hỗ trợ tốt nhất.</div>`;
const contentEn = `<h1 style="font-size: 24px; text-align: center;">PAYMENT PRIVACY POLICY</h1><div>Customers can make payments using the following two methods:
</div><div style="padding: 5px;"><b>1. Pay in cash when purchasing directly or upon delivery via shipping service.
</b></div><div style="padding: 5px;"><b>2. Bank transfer payment (contact Shop Lucky Vietnam for detailed instructions):
</b></div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>Account holder</li><li>Account number – Bank</li><li>Description: Customer phone number + Order ID
</li></ul><div><b style="text-decoration: underline;"></b>Note: Customers are advised to contact Shop Lucky Vietnam via hotline 0909 689 026 for the best support.
</div>`;
const contentCn = `<h1 style="font-size: 24px; text-align: center;">支付隐私政策</h1><div>客户可以通过以下两种方式付款：</div><div style="padding: 5px;"><b>1. 在店内购买时或通过快递收到货物时付款。</b></div><div style="padding: 5px;"><b>2. 银行转账付款（请联系 Shop Lucky 越南获取详细指导）：</b></div><ul style="display: flex; flex-direction: column; gap: 10px;"><li>账户持有人

</li><li>银行账户号码</li><li>备注：购买电话 + 订单编号</li></ul><div><b style="text-decoration: underline;"></b>注意：建议客户拨打热线 0909 689 026 联系 Shop Lucky 越南，以获得最佳支持。</div>`;

const PaymentPolicy: FC = () => {
  const [content, setContent] = useState(contentVi);
  const { i18n } = useTranslation();
  const { md } = Grid.useBreakpoint();

  useEffect(() => {
    if (i18n.language === 'vi') {
      setContent(contentVi);
    } else if (i18n.language === 'en') {
      setContent(contentEn);
    } else if (i18n.language === 'cn') {
      setContent(contentCn);
    }
  }, [i18n.language]);

  return (
    <Container
      style={{
        marginTop: md ? 120 : 40,
        position: md ? 'unset' : 'relative',
        backgroundColor: '#FFFFFF',
      }}
    >
      <div dangerouslySetInnerHTML={{ __html: content }}></div>
    </Container>
  );
};

export default PaymentPolicy;
