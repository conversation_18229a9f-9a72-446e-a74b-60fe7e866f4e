import Banner from '@/components/home/<USER>';
import CategoriesBySvg from '@/components/home/<USER>';
import Container from '@/components/home/<USER>';
import TopSection from '@/components/home/<USER>';
import ProductItem from '@/components/product/ProductItem';
import { textColorBase, white } from '@/constants/themeColor';
import { FireOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Flex, Grid, Row, Tabs, Spin } from 'antd';
import { CSSProperties, FC, useEffect, useRef, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, useNavigate, useParams } from 'react-router-dom';
import { useListProducts } from '../search-menu/hooks/useListProducts';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import { ROUTES } from '@/enums/Routes';
import { useListCategory } from './hooks/useListCategory';
import VoucherItem from '@/components/product/VoucherItem';
import { WinnersList } from '../../components/voucher/WinnerList';
import { VoucherList } from '@/components/voucher/VoucherList';
import PromoSection from '@/components/home/<USER>';
import BannerMob from '@/components/home/<USER>';

const wrapperStyles: React.CSSProperties = {
  background: white,
  margin: '10px 0',
  // padding: '16px 20px',
  borderRadius: 8,
};

const Voucher: FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { md, xs, xl } = Grid.useBreakpoint();
  const { categoryId } = useParams();
  const { data: listCategory } = useListCategory();
  const {
    data: listProducts,
    total,
    currentPage,
    handleChangePage,
  } = useListProducts(categoryId);
  const { startTransition } = useGlobalContext();
  const [loading, setLoading] = useState(false);

  const observer = useRef<IntersectionObserver | null>(null);
  const handleLoadMore = useCallback(() => {
    if (!loading && listProducts.length < total) {
      setLoading(true);
      handleChangePage(currentPage + 1);
    }
  }, [currentPage, handleChangePage, loading, listProducts.length, total]);
  
  const lastProductRef = useCallback((node: HTMLDivElement) => {
    if (observer.current) observer.current.disconnect();
    
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && listProducts.length < total) {
        handleLoadMore();
      }
    });
    
    if (node) observer.current.observe(node);
  }, [handleLoadMore, listProducts.length, total]);
  
  useEffect(() => {
    setLoading(false);
  }, [listProducts]);

  return (
    <>
      <Container
        style={{
          marginTop: md ? 120 : 40,
          position: md ? 'unset' : 'relative',
        }}
      >
        {!md && <PromoSection />}
        <CategoriesBySvg listCategory={listCategory} type="voucher" />
        <BannerMob />
        <WinnersList />
        <VoucherList />
      </Container>
      <Container style={{ paddingTop: 0 }}>
        <TopSection
          wrapperStyles={{
            ...wrapperStyles,
            background: white,
            // padding: xl ? 20 : 0,
          }}
          leftText={{ text: '' }}
          rightText={{
            text: '',
          }}
        >
          <Row gutter={[2, 2]}>
            {listProducts?.map((product, index) => (
              <Col
                key={index}
                xs={12}
                sm={12}
                md={8}
                lg={6}
                xl={4}
                xxl={4}
                style={{ padding: 2 }}
                ref={index === listProducts.length - 1 ? lastProductRef : null}
              >
                <VoucherItem item={{ ...product }} type="voucher" />
              </Col>
            ))}
          </Row>
          {loading && (
            <Flex justify="center" style={{ margin: '20px 0' }}>
              <Spin />
            </Flex>
          )}
        </TopSection>
      </Container>
    </>
  );
};

export default Voucher;
