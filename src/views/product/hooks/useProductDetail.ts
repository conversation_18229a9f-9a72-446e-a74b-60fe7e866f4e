import {
  ListUsersBuyedTicket,
  ProductDetailDto,
  ProductDetailReq,
} from '@/dto/product.dto';
import productsService from '@/services/products.service';
import { useCallback, useEffect, useState } from 'react';

export const useProductDetail = ({ id }: ProductDetailReq) => {
  const [data, setData] = useState<ProductDetailDto>();
  const [listBuyedTicket, setListBuyedTicket] =
    useState<ListUsersBuyedTicket>();

  const productDetail = useCallback(async () => {
    try {
      const res: ProductDetailDto = await productsService.detail({
        id,
      });
      setData(res);
    } catch (error) {}
  }, [id]);

  const buyedTicket = useCallback(async () => {
    try {
      const res: ListUsersBuyedTicket = await productsService.buyedTicketList({
        pageSize: 100,
        pageIndex: 1,
        productId:id,
      });
      setListBuyedTicket(res);
    } catch (error) {}
  }, [id]);

  useEffect(() => {
    productDetail();
  }, [productDetail]);

  useEffect(() => {
    buyedTicket();
  }, [buyedTicket]);

  return {
    data,
    listBuyedTicket,
  };
};
