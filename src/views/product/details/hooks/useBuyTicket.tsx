import { BuyTicketReq } from '@/dto/ticket.dto';
import { toastService } from '@/services/@common';
import ticketService from '@/services/ticket.service';
import { App } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

export const useBuyTicket = () => {
  const { t } = useTranslation();
  const { modal } = App.useApp();
  const buyTicket = useCallback(
    async (body: BuyTicketReq) => {
      try {
        const res = await ticketService.buyTicket(body);
        modal.success({
          title: t('ticket.message.buy_ticket_success.title'),
          content: (
            <div>
              <p>
                {t('ticket.message.buy_ticket_success.content', {
                  qty: res?.qty,
                  amountBuy: res?.amountBuy,
                })}
              </p>
            </div>
          ),
        });
      } catch (error) {
        toastService.handleError(error, t('error.deposit_coin'));
        console.log(`===== buyTicket error=====`, error);
      }
    },
    [modal, t]
  );

  return {
    buyTicket,
  };
};
