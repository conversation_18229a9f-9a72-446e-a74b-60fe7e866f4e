import { ROUTES } from '@/enums/Routes';
import { Breadcrumb, Col, Flex, Grid, Row, Tabs, Typography } from 'antd';
import { FC, startTransition, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  generatePath,
  Link,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom';
import { useProductDetail } from '../hooks/useProductDetail';
import ProductDetails from './ProductDetails';
import ProductReview from './ProductReview';
import Container from '@/components/home/<USER>';
import { white } from '@/constants/themeColor';
import PromoSection from '@/components/home/<USER>';
import { Text } from '@/components';

interface IndexProps {}

const Index: FC<IndexProps> = () => {
  const { id: productId } = useParams<{ id: string }>();
  const { t } = useTranslation();
  const { data: productDetail, listBuyedTicket } = useProductDetail({
    id: productId,
  });
  const { md } = Grid.useBreakpoint();
  const navigate = useNavigate();

  const onNavigateToProducts = () => {
    startTransition(() => {
      const url = generatePath(ROUTES.PRODUCT_LIST, {
        categoryId: 'all',
      });

      navigate(url);
    });
  };
  const breadcrumb = [
    {
      title: <Link to={ROUTES.HOME}>{t('header.home')}</Link>,
    },
    {
      title: (
        <p style={{ margin: 0 }} onClick={onNavigateToProducts}>
          {t('header.products')}
        </p>
      ),
    },
    {
      title: (
        <div className="truncate" style={{ maxWidth: '30vw' }}>
          {productDetail?.name}
        </div>
      ),
    },
  ];

  const [expanded, setExpanded] = useState(false);
  const [expandedTicket, setExpandedTicket] = useState(false);

  const toggleExpand = () => setExpanded(!expanded);
  const toggleExpandTicket = () => setExpandedTicket(!expandedTicket);
  const [searchParams] = useSearchParams();
  const isVoucher = searchParams.get('type') === 'voucher';
  const { TabPane } = Tabs;

  return (
    <Container
      style={{
        marginTop: md ? 120 : 40,
        marginBottom: md ? 20 : 0,
        position: md ? 'unset' : 'relative',
      }}
    >
      {!md && <PromoSection />}
      <Breadcrumb items={breadcrumb} style={{ marginBottom: 10 }} />
      <Flex vertical gap="large">
        <ProductDetails data={productDetail} isVoucher={isVoucher} />
        <Tabs defaultActiveKey="1">
          <TabPane
            key="1"
            tab={
              <Typography.Title
                level={5}
                style={{ margin: 0, color: '#F37421' }}
              >
                {t('product.description')}
              </Typography.Title>
            }
          >
            <Flex
              vertical
              style={{
                backgroundColor: white,
                padding: 20,
                borderRadius: 8,
                zIndex: 1,
              }}
            >
              {/* <Typography.Title level={5} style={{ margin: 0, marginBottom: 16 }}>
                {t('product.description')}
              </Typography.Title> */}
              <Typography.Paragraph
                ellipsis={!expanded ? { rows: 5, expandable: false } : false}
                style={{ marginBottom: 8 }}
              >
                <span
                  dangerouslySetInnerHTML={{ __html: productDetail?.desc }}
                />
              </Typography.Paragraph>

              {productDetail?.desc && productDetail?.desc.length > 100 && (
                <span
                  onClick={toggleExpand}
                  style={{
                    cursor: 'pointer',
                    display: 'inline-block',
                    marginTop: -10,
                    textDecoration: 'underline',
                  }}
                >
                  {expanded ? t('collapse') : t('see_more')}
                </span>
              )}
            </Flex>
          </TabPane>
          {isVoucher && (
            <TabPane
              key="2"
              tab={
                <Typography.Title
                  level={5}
                  style={{ margin: 0, color: '#F37421' }}
                >
                  {t('product.purchased_list')}
                </Typography.Title>
              }
            >
              <Row
                gutter={[5, 5]}
                style={{
                  backgroundColor: white,
                  padding: 20,
                  borderRadius: 8,
                  whiteSpace: 'nowrap',
                  maxHeight: '400px',
                  overflow: 'auto'
                }}
              >
                {listBuyedTicket?.data?.map((item, index) => (
                  <Col key={index}>
                    <Text>
                      {item.member.firstName}
                      {' - '}
                      <strong>
                        {item.qty} {t('home.ticket')}
                      </strong>
                    </Text>
                  </Col>
                ))}
                {/* <Col span={24}>
                  {listBuyedTicket?.data &&
                    listBuyedTicket?.data.length > 9 && (
                      <span
                        onClick={toggleExpandTicket}
                        style={{
                          cursor: 'pointer',
                          display: 'inline-block',
                          marginTop: -10,
                          textDecoration: 'underline',
                        }}
                      >
                        {!expandedTicket ? t('see_more') : t('collapse')}
                      </span>
                    )}
                </Col> */}
              </Row>
            </TabPane>
          )}
        </Tabs>
        {/* <ProductReview /> */}
      </Flex>
    </Container>
  );
};

export default Index;
