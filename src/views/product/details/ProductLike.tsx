import { Icon<PERSON>, Slick } from '@/components';
import { ResponsiveType } from '@/components/slick';
import { ROUTES } from '@/enums/Routes';
import { Flex, Typography } from 'antd';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, useNavigate } from 'react-router-dom';
import CardProduct from '../commons/CardProduct';
import Container from '@/components/home/<USER>';

interface ProductLikeProps {}

const ProductLike: FC<ProductLikeProps> = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const onViewAll = () => {
    // Do something
  };

  const onClickCard = (id: string) => {
    // Do something
    const url = generatePath(ROUTES.PRODUCT_DETAILS, { id });
    navigate(url);
    window.location.reload();
  };

  const responsive: ResponsiveType[] = [
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 10,
      },
    },
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 7,
      },
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 768 / 107 - 2,
      },
    },
    {
      breakpoint: 425,
      settings: {
        slidesToShow: 480 / 107 - 2,
      },
    },
    {
      breakpoint: 375,
      settings: {
        slidesToShow: 1,
      },
    },
  ];

  return (
    <Container>
      <Flex align="center" justify="space-between" style={{ width: '100%' }}>
        <Typography.Title level={5} style={{ margin: 0 }}>
          {t('product.also-like')}
        </Typography.Title>
        <Flex
          align="center"
          className="cursor-pointer"
          gap={6}
          onClick={onViewAll}
        >
          <Typography.Text className="green-primary">
            {t('product.view_all')}
          </Typography.Text>
          <Icons src="right-arrow" size={16} />
        </Flex>
      </Flex>
      <Slick responsive={responsive}>
        {new Array(10).fill(null).map((_, j) => (
          <CardProduct
            key={j}
            price={100000 + j}
            description={`Nho vườn giống mỹ lai thái lan ${j} `}
            onClick={onClickCard.bind(null, j.toString())}
          />
        ))}
      </Slick>
    </Container>
  );
};

export default ProductLike;
