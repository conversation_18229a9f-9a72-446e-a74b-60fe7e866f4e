import { DEVICES } from '@/enums/ScreenDevice';
import useResize from '@/hooks/useResize.hook';
import { Col, Row } from 'antd';
import { FC } from 'react';
import CardInfo from '../commons/CardInfo';
import Warehouse from '../commons/Warehouse';
import Container from '@/components/home/<USER>';

interface ProductInfoProps {}

const ProductInfo: FC<ProductInfoProps> = () => {
  const screen = useResize();

  return (
    <Container>
      <Row gutter={[16, 24]} align="middle">
        <Col
          sm={{ flex: '100%' }}
          md={{ flex: 'none' }}
          style={{ width: screen <= DEVICES.LAPTOP ? '100%' : 'fit-content' }}
        >
          <Warehouse name="Góc chợ xanh" warehouse="TP. Hồ Chí Minh" />
        </Col>
        <Col
          sm={{ flex: '100%' }}
          md={{ flex: 'auto' }}
          style={{ width: screen <= DEVICES.MOBILE ? '100%' : 'fit-content' }}
        >
          <Row justify="space-between" gutter={[24, 16]}>
            <Col flex="auto">
              <CardInfo
                icon="star"
                title="product.rating"
                subtitle="4.8/5"
                suffix="(20 đánh giá)"
              />
            </Col>
            <Col flex="auto">
              <CardInfo icon="mall" title="product.product" subtitle="120" />
            </Col>
            {screen > DEVICES.TABLET && (
              <>
                <Col flex="auto">
                  <CardInfo
                    icon="packaging"
                    title="product.preparation_time"
                    subtitle="3 giờ"
                  />
                </Col>
                <Col flex="auto">
                  <CardInfo
                    icon="clock-alarm"
                    title="product.response_time"
                    subtitle="60 phút"
                  />
                </Col>
                <Col flex="auto">
                  <CardInfo
                    icon="time"
                    title="product.joined"
                    subtitle="05/06/2021"
                  />
                </Col>
              </>
            )}
          </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default ProductInfo;
