import { Flex, Pagination, Typography } from 'antd';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import Comment from '../commons/Comment';
import { white } from '@/constants/themeColor';

interface ProductReviewProps {
  rating?: number;
  evaluate?: number;
}

const ProductReview: FC<ProductReviewProps> = ({
  rating = 0,
  evaluate = 0,
}) => {
  const { t } = useTranslation();

  const onPageChange = (page: number) => {
    // TODO: Fetch data
  };

  return (
    <div style={{ backgroundColor: white, padding: 20, borderRadius: 8 }}>
      <Flex align="center" gap={15} style={{ marginBottom: 16 }}>
        <Typography.Title level={5} style={{ margin: 0 }}>
          {t('product.product_reviews')}
        </Typography.Title>
        <Typography.Title
          className="green-primary"
          level={5}
          style={{ margin: 0 }}
        >
          {`${rating}/5`}
        </Typography.Title>
        <Typography.Text className="gray-darker">{`(${evaluate} ${t('product.evaluate')})`}</Typography.Text>
      </Flex>
      <Comment username="asdasdsa" />
      <Comment username="asdasdsa" />
      <Comment username="asdasdsa" />
      <Pagination
        align="end"
        defaultCurrent={1}
        total={50}
        onChange={onPageChange}
      />
    </div>
  );
};

export default ProductReview;
