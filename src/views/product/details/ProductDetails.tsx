import { QuantityInput } from '@/components';
import {
  redColor,
  white,
  yellowColor,
  yellowGradient,
} from '@/constants/themeColor';
import { ProductDetailDto } from '@/dto/product.dto';
import { DEVICES } from '@/enums/ScreenDevice';
import { useGlobalContext } from '@/hooks/useGlobalContext.hook';
import useResize from '@/hooks/useResize.hook';
import {
  Button,
  Card,
  Col,
  Flex,
  Grid,
  Image,
  Progress,
  Row,
  Typography,
} from 'antd';
import { CSSProperties, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { formatPriceVND, formatVND } from '@/utils/numberFormat';
import ProgressLine from '@/components/common/ProgressLine';
import { useBuyTicket } from './hooks/useBuyTicket';
import Container from '@/components/home/<USER>';
import ticketBg from '/images/olgr-dt-min.png';
import ImageProductCarousel from '@/components/product/ImageProductCarousel';
import PromoSection from '@/components/home/<USER>';
import ImageProductsSlider from '@/components/product/ImageProductsSlider';

const ActionStyles: CSSProperties = {
  width: 170,
  padding: '20px 10px',
  color: '#fff',
  backgroundColor: '#13A7B3',
  borderRadius: 0,
  marginBottom: 10,
};

const ticketSessions: CSSProperties = {
  background: `url(${ticketBg}) no-repeat`,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  padding: '10px 20px',
  marginTop: 20,
  borderRadius: 20,
  color: white,
};

export interface IProductDetailProps {
  data: ProductDetailDto;
  isVoucher?: boolean;
}

const ProductDetails = ({ data, isVoucher }: IProductDetailProps) => {
  const screen = useResize();
  const { t } = useTranslation();
  const { id: productId } = useParams<{ id: string }>();
  const { handleUpdateCart } = useGlobalContext();
  const { xl, md } = Grid.useBreakpoint();

  const { buyTicket } = useBuyTicket();

  const [quantity, setQuantity] = useState<number>(1);
  // const sessionId = data?.ticketSessions[0]?.id;

  const handleUpdate = (ticketId?: string) => {
    if (!productId) return;

    const item = {
      id: productId,
      quantity,
      ticketId,
      price: ticketId
        ? data?.ticketSessions[0].ticketPrice
        : +data?.buyNowPrice,
      cover: data.cover,
      name: data.name,
    };
    handleUpdateCart(item);
  };

  const handleBuyTicket = () => {
    buyTicket({
      sessionId: data?.ticketSessions[0]?.id,
      qty: quantity,
    });
  };

  const pipeImages = useCallback(() => {
    try {
      const res = [data?.cover, ...data?.images?.map((item) => item?.image)];
      return res;
    } catch (error) {
      return [];
    }
  }, [data?.cover, data?.images]);

  return (
    <div>
      <Row
        gutter={16}
        style={{
          backgroundColor: white,
          // padding: 20,
          borderRadius: 8,
          margin: 0,
        }}
      >
        {/* <Col xs={24} md={6} style={{ padding: 8 }}>
          <Card
            style={{
              display: 'flex',
              justifyContent: 'center',
              border: 'none',
            }}
            bodyStyle={{ padding: 0 }}
            cover={
              <Image
                loading="lazy"
                style={{ maxWidth: 400, maxHeight: 400 }}
                src={data?.cover}
              />
            }
          ></Card>
          {md && (
            <Flex justify="center" gap={5} style={{ marginTop: 5 }}>
              {(images || []).map((img, index) => (
                <Image
                  key={index}
                  src={img}
                  width={60}
                  height={60}
                  style={{
                    cursor: 'pointer',
                    background: '#F37421',
                    border: 'none',
                  }}
                />
              ))}
            </Flex>
          )}
          <ImageProductCarousel />
        </Col> */}
        <Col xs={24} md={12}  style={{ padding: 8 }}>
          <ImageProductsSlider images={pipeImages()} />
        </Col>
        <Col xs={24} md={12}>
          <Typography.Title level={4} style={{ margin: 0 }}>
            {data?.name}
          </Typography.Title>
          <Flex wrap align="center" gap="small" className="no-wrap">
            <Typography.Text className="gray-darker">
              {t('product.price')}
            </Typography.Text>
            <Typography.Text
              className="red-primary"
              style={{ fontWeight: 700, fontSize: 24 }}
            >
              {isVoucher
                ? `${data?.ticketSessions[0]?.ticketPrice} ${t('common.payment_currency')}`
                : formatPriceVND(data?.buyNowPrice)}
            </Typography.Text>
          </Flex>
          {/* <Typography.Text
            dangerouslySetInnerHTML={{ __html: data?.shortDesc }}
          ></Typography.Text> */}
          <div dangerouslySetInnerHTML={{ __html: data?.shortDesc }} />

          <Flex
            align={!md ? 'start' : 'center'}
            gap={10}
            style={{ flexDirection: !md ? 'column' : 'row', marginTop: 24 }}
          >
            <div>
              <Typography.Text className="no-wrap">
                {t('product.quantity')}
              </Typography.Text>
              <QuantityInput
                min={1}
                defaultValue={1}
                onChange={(value) => setQuantity(value)}
                style={{ width: 100, marginLeft: 16 }}
              />
            </div>
          </Flex>
          {isVoucher && (
            <>
              <ProgressLine
                className="custom-progress"
                current={data?.ticketSessions[0]?.soldTickets}
                total={data?.ticketSessions[0]?.totalTickets}
                styles={{ width: '100%', marginTop: 16 }}
              />
              <Typography.Text className="no-wrap" style={{ fontSize: 18 }}>
                <span>{t('product.remain')} </span>
                <span>
                  {data?.ticketSessions[0]?.totalTickets -
                    data?.ticketSessions[0]?.soldTickets}
                </span>
                <span> / </span>
                <span>{data?.ticketSessions[0]?.totalTickets}</span>
                <span> {t('product.ticket')}</span>
              </Typography.Text>
            </>
          )}
          <Flex wrap gap={5} style={{ marginTop: 24, flexWrap: 'nowrap' }}>
            {!isVoucher && (
              <>
                <Button
                  className="cart-button"
                  style={{ ...ActionStyles }}
                  onClick={handleUpdate.bind(null, undefined)}
                >
                  {t('product.add_to_cart')}
                </Button>
              </>
            )}
            {isVoucher && (
              <Button
                style={{
                  ...ActionStyles,
                  backgroundColor: '#F37421',
                  color: '#fff',
                }}
                onClick={handleBuyTicket}
              >
                {t('product.buy_with_voucher')}
              </Button>
            )}
          </Flex>
        </Col>
      </Row>
    </div>
  );
};

export default ProductDetails;
