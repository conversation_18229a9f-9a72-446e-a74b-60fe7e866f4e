import { DEVICES } from '@/enums/ScreenDevice';
import useResize from '@/hooks/useResize.hook';
import { Flex, Image, Typography } from 'antd';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import ActionButton from './ActionButton';

interface WarehouseProps {
  name?: string;
  warehouse?: string;
}

const Warehouse: FC<WarehouseProps> = ({ name = '', warehouse = '' }) => {
  const screen = useResize();
  const { t } = useTranslation();

  const onChat = () => {
    // TODO: Implement chat now
  };

  const onView = () => {
    // TODO: Implement chat now
  };

  return (
    <Flex
      vertical={screen > DEVICES.LAPTOP}
      gap={16}
      align="center"
      justify="space-between"
      style={{ width: '100%' }}
    >
      <Flex gap={12}>
        <Image
          loading="lazy"
          preview={false}
          width={45}
          height={45}
          src="/images/category-mobile.png"
        />
        <Flex vertical justify="space-between" align="start">
          <Typography.Title
            level={5}
            className="green-primary"
            style={{ margin: 0 }}
          >
            {name}
          </Typography.Title>
          <Typography.Text className="green-primary">
            <Typography.Text
              className="gray-darker"
              style={{ marginRight: 8 }}
            >{`${t('product.warehouse')}:`}</Typography.Text>
            {warehouse}
          </Typography.Text>
        </Flex>
      </Flex>
      <Flex gap="middle" wrap="wrap">
        {screen > DEVICES.MOBILE && (
          <ActionButton
            color="#FF7F23"
            label="product.chat_now"
            icon="chat"
            onCLick={onChat}
          />
        )}
        <ActionButton
          color="#0AAD0A"
          label="product.view_shop"
          icon="mall-orange"
          onCLick={onView}
        />
      </Flex>
    </Flex>
  );
};

export default Warehouse;
