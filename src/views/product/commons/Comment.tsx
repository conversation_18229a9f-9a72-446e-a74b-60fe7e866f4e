import { Avatar, Divider, Flex, Image, Rate, Typography } from 'antd';
import { FC } from 'react';

interface CommentProps {
  avatar?: string;
  username?: string;
  rating?: number;
  comment?: string;
  images?: string[];
}

const Comment: FC<CommentProps> = ({
  avatar = '/images/category-head-speaker.png',
  username,
  rating,
  comment,
  images,
}) => {
  return (
    <Flex gap={16}>
      <div style={{ width: 32 }}>
        <Avatar size={32} src={avatar} />
      </div>
      <div style={{ flexGrow: 1 }}>
        <Flex align="center" gap={5} style={{ marginBottom: 16 }}>
          <Typography.Text className="gray-darker">{username}</Typography.Text>
          <Rate
            allowHalf
            disabled
            defaultValue={rating}
            style={{ marginLeft: 8 }}
          />
        </Flex>
        <Flex vertical gap={16}>
          <Typography.Text className="gray-darker">
            ngon, giao hàng nhanh đóng gói cẩn thận, hàng y hình, gi<PERSON><PERSON> quảng
            cáo, gi<PERSON> cả vừa phải, mua chung rẻ hơn nhiều so với mua riêng
          </Typography.Text>
          <Flex align="center" gap={5}>
            <Image
              loading="lazy"
              width={80}
              height={80}
              src="/images/category-mobile.png"
            />
            <Image
              loading="lazy"
              width={80}
              height={80}
              src="/images/category-mobile.png"
            />
            <Image
              loading="lazy"
              width={80}
              height={80}
              src="/images/category-mobile.png"
            />
          </Flex>
        </Flex>
        <Divider />
      </div>
    </Flex>
  );
};

export default Comment;
