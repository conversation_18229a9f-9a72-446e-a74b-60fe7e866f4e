import Icon, { Icons } from '@/components/icons';
import { Button } from 'antd';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface ActionButtonProps {
  icon: Icons;
  color: string;
  label: string;
  onCLick: () => void;
}

const ActionButton: FC<ActionButtonProps> = ({
  icon,
  color,
  label,
  onCLick,
}) => {
  const { t } = useTranslation();

  return (
    <Button
      icon={<Icon src={icon} />}
      style={{ color, borderColor: color, fontWeight: 700, padding: '0 6px' }}
      onClick={onCLick}
    >
      {t(label)}
    </Button>
  );
};

export default ActionButton;
