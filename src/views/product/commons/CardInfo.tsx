import Icon, { Icons } from '@/components/icons';
import { Flex, Grid, Typography } from 'antd';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface CardInfoProps {
  icon: Icons;
  title?: string;
  subtitle?: string;
  suffix?: string;
}

const CardInfo: FC<CardInfoProps> = ({
  title = '',
  subtitle = '',
  suffix = '',
  icon,
}) => {
  const { t } = useTranslation();
  const screens = Grid.useBreakpoint();

  return (
    <Flex vertical align="center" justify="space-between" gap={8}>
      <Flex align="center" gap={8}>
        <Icon src={icon} />
        <Typography.Text className="gray-darker no-wrap">
          {t(title)}
        </Typography.Text>
      </Flex>
      <Flex align="center" gap={8}>
        <Typography.Text className="green-primary no-wrap">
          {t(subtitle)}
        </Typography.Text>
        <Typography.Text className="gray-darker no-wrap">
          {t(suffix)}
        </Typography.Text>
      </Flex>
    </Flex>
  );
};

export default CardInfo;
