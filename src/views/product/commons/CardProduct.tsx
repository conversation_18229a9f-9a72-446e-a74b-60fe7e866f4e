import { Card, Statistic, Typography } from 'antd';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface CardProductProps {
  price?: number;
  description?: string;
  onClick?: () => void;
}

const CardProduct: FC<CardProductProps> = ({ price, description, onClick }) => {
  const { t } = useTranslation();

  return (
    <Card
      hoverable
      style={{ width: 107, height: 176 }}
      cover={
        <img
          alt="example"
          loading="lazy"
          src="/images/category-mobile.png"
          height={107}
          width={107}
        />
      }
      styles={{
        body: {
          padding: 5,
        },
      }}
      onClick={onClick}
    >
      <Typography.Paragraph
        style={{ fontSize: 12, height: 38, marginBottom: 4 }}
        ellipsis={{
          rows: 2,
        }}
      >
        {description}
      </Typography.Paragraph>
      <Statistic
        value={112893}
        precision={2}
        suffix={t('currency')}
        valueStyle={{
          fontSize: 12,
          color: '#ff0909',
        }}
      />
    </Card>
  );
};

export default CardProduct;
