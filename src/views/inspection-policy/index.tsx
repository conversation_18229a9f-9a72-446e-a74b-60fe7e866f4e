import { FC, useEffect, useState } from 'react';
import Container from '@/components/home/<USER>';
import { Grid } from 'antd';
import { useTranslation } from 'react-i18next';

const contentVi = `<h1 style="font-size: 24px; text-align: center;">CHÍNH SÁCH KIỂM HÀNG</h1><ul style="display: flex; flex-direction: column; gap: 10px;"><li>Khi nhận hàng quý khách có quyền yêu cầu nhân viên giao hàng mở cho kiểm rồi mới nhận hàng.</li><li>Trường hợp đơn hàng đặt mà bên bán giao không đúng loại sản phẩm quý khách có quyền trả hàng và không không thanh toán tiền.</li><li><PERSON><PERSON><PERSON><PERSON><PERSON> hợp quý khách đã thanh toán trước nhưng đơn hàng không đúng quý khách yêu cầu hoàn tiền hoặc giao lại đơn mới như đã đặt.</li><li>Trong trường hợp yêu cầu hoàn tiền hoặc đổi đơn quý khách liên hệ qua số điện thoại: 0909 689 026 chúng tôi cam kết sẽ giải quyết mọi yêu cầu của quý khách.</li></ul>`;
const contentEn = `<h1 style="font-size: 24px; text-align: center;">INSPECTION POLICY</h1><ul style="display: flex; flex-direction: column; gap: 10px;"><li>Upon delivery, customers have the right to request the delivery staff to open and inspect the goods before accepting them.</li><li>If the delivered product does not match the order, the customer has the right to return the item and is not required to make any payment.
</li><li>If the customer has paid in advance but receives the wrong product, they may request a refund or a replacement order as originally placed.
</li><li>For refund or replacement requests, please contact us at 0909 689 026. We are committed to resolving all customer inquiries promptly.
</li></ul>`;
const contentCn = `<h1 style="font-size: 24px; text-align: center;">验货政策</h1><ul style="display: flex; flex-direction: column; gap: 10px;"><li> 收货时，客户有权要求送货员开箱验货后再签收。</li><li>若卖方发错产品，客户有权退货且无需付款。</li><li>若客户已提前付款但收到的产品与订单不符，客户可要求退款或重新发货。</li><li>如需退款或更换订单，请致电 0909 689 026 联系我们，我们承诺将尽快处理您的请求。</li></ul>`;

const InspectionPolicy: FC = () => {
  const [content, setContent] = useState(contentVi);
  const { i18n } = useTranslation();
  const { md } = Grid.useBreakpoint();

  useEffect(() => {
    if (i18n.language === 'vi') {
      setContent(contentVi);
    } else if (i18n.language === 'en') {
      setContent(contentEn);
    } else if (i18n.language === 'cn') {
      setContent(contentCn);
    }
  }, [i18n.language]);

  return (
    <Container
      style={{
        marginTop: md ? 120 : 40,
        position: md ? 'unset' : 'relative',
        backgroundColor: '#FFFFFF',
      }}
    >
      <div dangerouslySetInnerHTML={{ __html: content }}></div>
    </Container>
  );
};

export default InspectionPolicy;
