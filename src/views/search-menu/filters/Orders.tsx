import { radius, textColorBase, white } from '@/constants/themeColor';
import { <PERSON><PERSON>, Drawer, Flex, Grid } from 'antd';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FilterOutlined } from '@ant-design/icons';
import ByPrice from './ByPrice';

interface IOrdersProps {}
const ordersStyles: React.CSSProperties = {
  color: textColorBase,
  borderRadius: radius,
  background: white,
};
const filterItems: React.CSSProperties = {
  color: ' #0AAD0A',
  borderRadius: radius,
  padding: '6px 13px',
  background: '#E3FFE5',
  cursor: 'pointer',
  width: 42,
  height: 42,
  marginLeft: 'auto',
  fontSize: 14,
  fontWeight: 400,
};

const Orders: FC<IOrdersProps> = ({}) => {
  const { t } = useTranslation();
  const { xl } = Grid.useBreakpoint();
  const [open, setOpen] = useState(false);
  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  return (
    <div
      style={{
        ...ordersStyles,
        padding: xl ? 20 : 10,
        marginBottom: xl ? 20 : 16,
      }}
    >
      <Flex gap={10} align="center">
        {xl ? (
          <>
            <div style={{ marginRight: 20 }}>{t('search.orderBy')}</div>
            <Button type="primary">{t('search.bestSell')}</Button>
            <Button>{t('search.latest')}</Button>
            {/* <Button>{t('search.lowPrice')}</Button>
            <Button>{t('search.highPrice')}</Button> */}
          </>
        ) : (
          <>
            <Button size="large" type="primary">
              {t('search.bestSell')}
            </Button>
            <span>{t('search.latest')}</span>
            <span>{t('search.lowPrice')}</span>
            <Flex
              style={filterItems}
              vertical
              align="center"
              onClick={showDrawer}
            >
              <FilterOutlined />
              <span>Lọc</span>
            </Flex>
            <Drawer
              width={'65%'}
              title={t('search.productsFilter')}
              onClose={onClose}
              open={open}
            >
              <ByPrice />
            </Drawer>
          </>
        )}
      </Flex>
    </div>
  );
};

export default Orders;
