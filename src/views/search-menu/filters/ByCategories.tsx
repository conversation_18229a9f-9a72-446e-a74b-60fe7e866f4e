import { textColorBase } from '@/constants/themeColor';
import { useListCategory } from '@/views/home/<USER>/useListCategory';
import { Grid, List } from 'antd';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useFilters } from '../hooks/useFilters';
import { useContentLang } from '@/hooks/useContentLang';

interface IByCategoriesProps {
  filters?: {
    categoryId?: string;
  };
  onChange?: any;
}

const categoriesTitle: React.CSSProperties = {
  color: textColorBase,
  fontWeight: 'bold',
  marginBlockEnd: '1rem',
};

const ByCategoryDetail = ({ onChange, item }) => {
  const onChangeCategory = (category) => {
    onChange({ categoryId: category.id });
  };
  const name = useContentLang(item);
  return (
    <List.Item
      onClick={() => onChangeCategory(item)}
      style={{ cursor: 'pointer' }}
    >
      {name}
    </List.Item>
  );
};

const ByCategories: FC<IByCategoriesProps> = ({ onChange }) => {
  const { t } = useTranslation();
  const { xl } = Grid.useBreakpoint();
  const { data: listCategory } = useListCategory();
  const [categorySelected, setCategorySelected] = useState(null);

  const { handleCategoryChange } = useFilters();

  return (
    <div>
      <div style={{ ...categoriesTitle, fontSize: xl ? '1.5rem' : 14 }}>
        {t('search.byCategories')}
      </div>
      <List
        grid={{ column: 1 }}
        dataSource={listCategory}
        renderItem={(item) => (
          <div
            style={{
              padding: '5px',
              cursor: 'pointer',
              backgroundColor:
                categorySelected === item ? '#f0f0f0' : 'transparent',
              borderRadius: '4px',
              transition: 'background-color 0.3s ease',
            }}
          >
            <ByCategoryDetail
              onChange={() => handleCategoryChange(item.id)}
              item={item}
            />
          </div>
        )}
      />
    </div>
  );
};

export default ByCategories;
