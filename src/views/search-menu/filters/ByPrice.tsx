import { textColorBase } from '@/constants/themeColor';
import { inputNumberFormatter } from '@/utils/numberFormat';
import { Button, Flex, Grid, InputNumber } from 'antd';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface IByPriceProps {}

const title: React.CSSProperties = {
  color: textColorBase,
  fontWeight: 700,
  marginBlockEnd: '1rem',
};

const ByPrice: FC<IByPriceProps> = ({}) => {
  const { xl } = Grid.useBreakpoint();
  const { t } = useTranslation();

  return (
    <div>
      <div style={{ ...title, fontSize: xl ? '1.5rem' : 14 }}>
        {t('search.price')}
      </div>
      <Flex justify="space-between" align="center" gap={5}>
        <InputNumber
          placeholder={t('search.from')}
          style={{ flex: 1 }}
          formatter={inputNumberFormatter as any}
          min={0}
          defaultValue={0}
        />
        <span>-</span>
        <InputNumber
          formatter={inputNumberFormatter as any}
          placeholder={t('search.to')}
          style={{ flex: 1 }}
          defaultValue={100000}
        />
      </Flex>
      <Button style={{ width: '100%', marginTop: 10 }}>
        {t('search.apply')}
      </Button>
    </div>
  );
};

export default ByPrice;
