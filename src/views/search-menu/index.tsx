import { Breadcrumbs } from '@/components';
import ProductItem from '@/components/product/ProductItem';
import { Col, Flex, Grid, Row, Spin } from 'antd';
import { FC, useRef, useCallback, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useParams } from 'react-router-dom';
import Container from '../../components/home/<USER>';
import ByCategories from './filters/ByCategories';
import { useListProducts } from './hooks/useListProducts';
import { useFilters } from './hooks/useFilters';
import PromoSection from '@/components/home/<USER>';
import CategoriesBySvg from '@/components/home/<USER>';
import { useListCategory } from '../home/<USER>/useListCategory';

interface SearchMenuProps {}

const SearchMenu: FC<SearchMenuProps> = () => {
  const { xl, xs, lg, md } = Grid.useBreakpoint();
  const { t } = useTranslation();
  const { filters, handleCategoryChange } = useFilters();
  const { categoryId } = useParams();
  const { data, total, currentPage, handleChangePage } =
    useListProducts(categoryId);
  const { data: listCategory } = useListCategory();
  const [loading, setLoading] = useState(false);

  const observer = useRef<IntersectionObserver | null>(null);
  const handleLoadMore = useCallback(() => {
    if (!loading && data.length < total) {
      setLoading(true);
      handleChangePage(currentPage + 1);
    }
  }, [currentPage, handleChangePage, loading, data.length, total]);

  const lastProductRef = useCallback(
    (node: HTMLDivElement) => {
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && data.length < total) {
          handleLoadMore();
        }
      });

      if (node) observer.current.observe(node);
    },
    [handleLoadMore, data.length, total]
  );

  useEffect(() => {
    setLoading(false);
  }, [data]);

  const crumbs = [
    {
      title: <Link to="/">{t('crumbs.home')}</Link>,
    },
    {
      title: <Link to="/">{t('crumbs.categories')}</Link>,
    },
  ];

  return (
    <Container
      style={{ marginTop: md ? 120 : 50, position: md ? 'unset' : 'relative' }}
    >
      {md && (
        <Breadcrumbs styles={{ marginBottom: xl ? 20 : 10 }} items={crumbs} />
      )}
      {!md && <PromoSection />}
      {!md && <CategoriesBySvg listCategory={listCategory} />}

      <Flex gap={20} vertical={!xl}>
        {xl && (
          <Col flex={'0 1 270px'}>
            <ByCategories
              onChange={(e) => handleCategoryChange(e.categoryId)}
            />
            {/* <ByPrice /> */}
          </Col>
        )}

        <Col flex={'1 1'}>
          {/* <Row gutter={md ? [16, 10] : [5, 5]}> */}
          <Row gutter={[2, 2]}>
            {data?.map((product, index) => (
              <Col
                key={index}
                xs={12}
                sm={8}
                md={8}
                lg={6}
                xl={6}
                xxl={5}
                ref={index === data.length - 1 ? lastProductRef : null}
                style={{ padding: 2 }}
              >
                <ProductItem item={{ ...product }} />
              </Col>
            ))}
          </Row>
          {loading && (
            <Flex justify="center" style={{ margin: '20px 0' }}>
              <Spin />
            </Flex>
          )}
        </Col>
      </Flex>
    </Container>
  );
};

export default SearchMenu;
