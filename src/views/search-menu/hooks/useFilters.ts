import { ROUTES } from '@/enums/Routes';
import { startTransition, useState } from 'react';
import { generatePath, useNavigate, useParams } from 'react-router-dom';

interface IFilters {
  categoryId?: string;
  selectedCategoryId?: string;
}

export const useFilters = () => {
  const [filters, setFilters] = useState<IFilters>({ categoryId: null });
  const navigate = useNavigate();
  const { categoryId } = useParams();
  const handleCategoryChange = (newCategoryId?: string, type?: string) => {
    if (type === 'voucher') {
      startTransition(() => {
        const url = generatePath(ROUTES.VOUCHER, {
          categoryId: newCategoryId.toString(),
        });
        navigate(url);
      });
    } else {
      startTransition(() => {
        const url = generatePath(ROUTES.PRODUCT_LIST, {
          categoryId: newCategoryId.toString(),
        });
        navigate(url);
      });
    }
  };


  return {
    filters,
    handleCategoryChange,
    selectedCategoryId: categoryId,
  };
};
