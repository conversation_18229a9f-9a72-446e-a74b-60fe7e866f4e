import productsService from '@/services/products.service';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const useListProducts = (categoryId?: string) => {
  const [data, setData] = useState([]);
  const [listTrending, setListTrending] = useState([]);
  const [listFlashSale, setFlashSale] = useState([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const { i18n } = useTranslation();
  
  // Reset to page 1 when categoryId changes
  useEffect(() => {
    setCurrentPage(1);
  }, [categoryId]);
  
  const handleChangePage = (currentPage: number) => {
    setCurrentPage(currentPage);
  };

  const listProducts = useCallback(async () => {
    try {
      const res = await productsService.list({
        pageIndex: currentPage,
        pageSize: 12,
        categoryId: categoryId === 'all' ? null : categoryId,
      });
      
      // Reset data when category changes (currentPage = 1)
      // Accumulate data when loading more (currentPage > 1)
      if (currentPage > 1) {
        setData(prevData => [...prevData, ...(res?.data || [])]);
      } else {
        setData(res?.data || []);
      }
      
      setTotal(res?.total || 0);
    } catch (error) {}
  }, [categoryId, currentPage]);

  const listProductsTrending = useCallback(async () => {
    try {
      const res = await productsService.list({
        pageIndex: currentPage,
        pageSize: 12,
        sectionCode: 'TRENDING',
      });
      setListTrending(res?.data || []);
    } catch (error) {}
  }, []);

  const listProductsFlashSale = useCallback(async () => {
    try {
      const res = await productsService.list({
        pageIndex: currentPage,
        pageSize: 12,
        sectionCode: 'FLASH_SALE',
      });
      setFlashSale(res?.data || []);
    } catch (error) {}
  }, []);

  useEffect(() => {
    listProducts();
  }, [listProducts]);

  useEffect(() => {
    listProductsTrending();
  }, [listProductsTrending]);

  useEffect(() => {
    listProductsFlashSale();
  }, [listProductsFlashSale]);

  return {
    data,
    total,
    currentPage,
    listTrending,
    listFlashSale,
    handleChangePage,
  };
};
