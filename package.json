{"name": "happy-shop-web", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "eslintConfig": {"extends": ["react-app"], "rules": {"no-extend-native": 0, "array-callback-return": 0, "@typescript-eslint/no-unused-vars": 0, "no-useless-escape": 0, "react-hooks/exhaustive-deps": 1, "jsx-a11y/anchor-is-valid": 0}}, "dependencies": {"antd": "^5.22.6", "axios": "^1.7.9", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "i18next": "^24.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.3.0", "react-router-dom": "^6.0.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "typescript": "^5.7.2"}, "devDependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@types/dotenv": "^6.1.1", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "prettier": "^3.4.2", "sass": "^1.83.0", "vite": "^6.0.6"}}